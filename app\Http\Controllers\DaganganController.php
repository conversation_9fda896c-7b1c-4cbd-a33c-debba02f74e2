<?php

namespace App\Http\Controllers;

use App\Models\Dagangan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class DaganganController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $dagangans = Dagangan::latest()->get();
        return view('pages.dagangan.index', compact('dagangans'));
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nama_dagangan' => 'required|string|max:255|unique:dagangans,nama_dagangan'
        ], [
            'nama_dagangan.required' => 'Nama dagangan wajib diisi',
            'nama_dagangan.unique' => 'Nama dagangan sudah ada'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            Dagangan::create([
                'nama_dagangan' => $request->nama_dagangan
            ]);

            return response()->json([
                'success' => 'Data dagangan berhasil ditambahkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menyimpan data'
            ], 500);
        }
    }


    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Dagangan $dagangan)
    {
        return response()->json($dagangan);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Dagangan $dagangan)
    {
        $validator = Validator::make($request->all(), [
            'nama_dagangan' => 'required|string|max:255|unique:dagangans,nama_dagangan,' . $dagangan->id
        ], [
            'nama_dagangan.required' => 'Nama dagangan wajib diisi',
            'nama_dagangan.unique' => 'Nama dagangan sudah ada'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $dagangan->update([
                'nama_dagangan' => $request->nama_dagangan
            ]);

            return response()->json([
                'success' => 'Data dagangan berhasil diperbarui'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat memperbarui data'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Dagangan $dagangan)
    {
        // try {
            $dagangan->delete();

            return response()->json([
                'success' => 'Data dagangan berhasil dihapus'
            ]);
        // } catch (\Exception $e) {
        //     return response()->json([
        //         'error' => 'Terjadi kesalahan saat menghapus data'
        //     ], 500);
        // }
    }
}
