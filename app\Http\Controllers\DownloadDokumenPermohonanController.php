<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Permohonan;
use Illuminate\Http\Request;

class DownloadDokumenPermohonanController extends Controller
{
    public function baru($id, $jenis)
    {
        $permohonanBaru = Permohonan::with(['dagangan', 'lapak.pasar','lapak.pasar.user', 'lapak'])
            ->findOrFail($id);

        $templateView = match ($jenis) {
            'ba_penunjukan'     => 'pages.permohonan.dokumen.ba-penunjukan',
            'surat_pernyataan'  => 'pages.permohonan.dokumen.surat-pernyataan',
            'sp_pemilik'       => 'pages.permohonan.dokumen.sp-pemilik',
            default             => abort(404, 'Jenis dokumen tidak ditemukan.'),
        };

        $hari = ['Minggu', 'Senin', '<PERSON>lasa', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Sabtu'];
        $bulan = [
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            'April',
            '<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            'September',
            '<PERSON><PERSON><PERSON>',
            'November',
            '<PERSON>ember'
        ];

        $now = Carbon::now();
        $hariIni = $hari[$now->dayOfWeek];
        $tanggal = $now->day . ' ' . $bulan[$now->month - 1] . ' ' . $now->year;


        return view($templateView, compact(
            'permohonanBaru',
            'hariIni',
            'tanggal',
        ));
    }
}
