<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class KepalaDinasController extends Controller
{
    public function index()
    {
        $kepalaDinas = User::where('role', 'kepala_dinas')->latest()->get();

        return view('pages.kepala_dinas.index', compact('kepalaDinas'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nama' => 'required|string|max:255',
            'nip' => 'required|string|max:255|regex:/^[0-9]+$/',
            'golongan' => 'required|string|max:255',
            'periode' => 'required|string|max:255',
            'username' => 'required|string|max:50|unique:users,username',
            'password' => 'required|string|min:8'
        ], [
            'nama.required' => 'Nama wajib diisi',
            'nip.required' => 'NIP wajib diisi',
            'nip.numeric' => 'NIP harus berupa angka',
            'golongan.required' => 'Golongan wajib diisi',
            'periode.required' => 'Periode wajib diisi',
            'status.in' => 'Status tidak valid',
            'username.required' => 'Username wajib diisi',
            'username.unique' => 'Username sudah digunakan',

            'password.required' => 'Password wajib diisi',
            'password.min' => 'Password minimal 8 karakter'
        ]);
        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }
        try {
            User::where('role', 'kepala_dinas')
                ->where('status', 'aktif')
                ->update([
                    'status' => 'nonaktif'
                ]);

            User::create([
                'role' => 'kepala_dinas',
                'nama' => $request->nama,
                'nip' => $request->nip,
                'golongan' => $request->golongan,
                'periode' => $request->periode,
                'status' => 'aktif',
                'username' => $request->username,
                'password' => Hash::make($request->password)
            ]);

            return response()->json([
                'success' => 'Data kepala dinas berhasil ditambahkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menyimpan data'
            ], 500);
        }
    }

    public function edit(User $kepalaDinas)
    {
        return response()->json($kepalaDinas);
    }

    public function update(Request $request, User $kepalaDinas)
    {
        $validator = Validator::make($request->all(), [
            'nama' => 'required|string|max:255',
            'nip' => 'required|string|max:255',
            'golongan' => 'required|string|max:255',
            'periode' => 'required|string|max:255',
            'status' => 'required|in:aktif,nonaktif',
            'username' => 'required|string|max:50|unique:users,username,' . $kepalaDinas->id,
            'password' => 'nullable|string|min:8'
        ], [
            'nama.required' => 'Nama wajib diisi',
            'nip.required' => 'NIP wajib diisi',
            'golongan.required' => 'Golongan wajib diisi',
            'periode.required' => 'Periode wajib diisi',
            'status.required' => 'Status wajib dipilih',
            'status.in' => 'Status tidak valid',
            'password.min' => 'Password minimal 8 karakter'
        ]);
        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = [
                'nama' => $request->nama,
                'nip' => $request->nip,
                'golongan' => $request->golongan,
                'periode' => $request->periode,
                'status' => $request->status,
                'username' => $request->username
            ];

            if ($request->filled('password')) {
                $data['password'] = Hash::make($request->password);
            }

            $kepalaDinas->update($data);

            return response()->json([
                'success' => 'Data kepala dinas berhasil diperbarui'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat memperbarui data'
            ], 500);
        }
    }

    public function destroy(User $kepalaDinas)
    {
        try {
            $kepalaDinas->delete();
            return response()->json([
                'success' => 'Data kepala dinas berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menghapus data'
            ], 500);
        }
    }
}
