<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Pasar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class KepalaPasarController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pasars = Pasar::orderBy('nama_pasar', 'asc')->get();
        $kepalaPasar = User::with('pasar')
            ->where('role', 'kepala_pasar')
            ->latest()
            ->get();
        return view('pages.kepala_pasar.index', compact('kepalaPasar', 'pasars'));
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pasar_id' => 'required|exists:pasars,id',
            'nama' => 'required|string|max:255',
            'nip' => 'required|string|max:30',
            'username' => 'required|string|max:50|unique:users,username',
            'password' => 'required|string|min:6'
        ], [
            'pasar_id.required' => 'Pasar wajib dipilih',
            'pasar_id.exists' => 'Pasar tidak valid',
            'nama.required' => 'Nama wajib diisi',
            'nip.required' => 'NIP wajib diisi',
            'username.required' => 'Username wajib diisi',
            'username.unique' => 'Username sudah digunakan',
            'password.required' => 'Password wajib diisi',
            'password.min' => 'Password minimal 6 karakter',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            User::create([
                'pasar_id' => $request->pasar_id,
                'role' => 'kepala_pasar',
                'nama' => $request->nama,
                'nip' => $request->nip,
                'username' => $request->username,
                'password' => Hash::make($request->password),
            ]);

            return response()->json([
                'success' => 'Data kepala pasar berhasil ditambahkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menyimpan data'
            ], 500);
        }
    }



    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $kepalaPasar)
    {
        return response()->json($kepalaPasar->load('pasar'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $kepalaPasar)
    {
        $validator = Validator::make($request->all(), [
            'pasar_id' => 'required|exists:pasars,id',
            'nama' => 'required|string|max:255',
            'nip' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username,' . $kepalaPasar->id,
            'status' => 'required|in:aktif,nonaktif',
            'password' => 'nullable|string|min:6'
        ], [
            'pasar_id.required' => 'Pasar wajib dipilih',
            'pasar_id.exists' => 'Pasar tidak valid',
            'nama.required' => 'Nama wajib diisi',
            'nip.required' => 'NIP wajib diisi',
            'username.required' => 'Username wajib diisi',
            'username.unique' => 'Username sudah digunakan',
            'status.required' => 'Status wajib dipilih',
            'status.in' => 'Status tidak valid',
            'password.min' => 'Password minimal 6 karakter'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = [
                'pasar_id' => $request->pasar_id,
                'nama' => $request->nama,
                'nip' => $request->nip,
                'username' => $request->username,
                'status' => $request->status
            ];

            if ($request->filled('password')) {
                $data['password'] = bcrypt($request->password);
            }

            $kepalaPasar->update($data);

            return response()->json([
                'success' => 'Data kepala pasar berhasil diperbarui'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat memperbarui data'
            ], 500);
        }
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $kepalaPasar)
    {
        try {
            $kepalaPasar->delete();
            return response()->json([
                'success' => 'Data kepala pasar berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menghapus data'
            ], 500);
        }
    }
}
