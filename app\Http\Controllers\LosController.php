<?php

namespace App\Http\Controllers;

use App\Models\Lapak;
use App\Models\Pasar;
use App\Models\Tarif;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LosController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $los = Lapak::with(['pasar', 'tarif'])->los()->latest()->get();
        $pasars = Pasar::all();
        $tarifs = Tarif::where('jenis', 'Los')->get();

        return view('pages.los.index', compact('los', 'pasars', 'tarifs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pasar_id' => 'required|exists:pasars,id',
            'tarif_id' => 'required|exists:tarifs,id',
            'nomor' => 'required|string|max:255',
            'nama_blok' => 'required|string|max:255',
            'lokasi' => 'nullable|string|max:255',
            'lebar' => 'required|numeric|min:0',
            'panjang' => 'required|numeric|min:0',
        ], [
            'pasar_id.required' => 'Pasar wajib dipilih',
            'pasar_id.exists' => 'Pasar tidak valid',
            'tarif_id.required' => 'Tarif wajib dipilih',
            'tarif_id.exists' => 'Tarif tidak valid',
            'nomor.required' => 'Nomor los wajib diisi',
            'nama_blok.required' => 'Nama blok wajib diisi',
            'lebar.required' => 'Lebar wajib diisi',
            'lebar.numeric' => 'Lebar harus berupa angka',
            'lebar.min' => 'Lebar tidak boleh kurang dari 0',
            'panjang.required' => 'Panjang wajib diisi',
            'panjang.numeric' => 'Panjang harus berupa angka',
            'panjang.min' => 'Panjang tidak boleh kurang dari 0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            Lapak::create([
                'pasar_id' => $request->pasar_id,
                'tarif_id' => $request->tarif_id,
                'jenis' => 'los',
                'nomor' => $request->nomor,
                'nama_blok' => $request->nama_blok,
                'lokasi' => $request->lokasi,
                'lebar' => $request->lebar,
                'panjang' => $request->panjang,
                'status' => 'tersedia'
            ]);

            return response()->json([
                'success' => 'Data los berhasil ditambahkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menyimpan data'
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Lapak $los)
    {
        return response()->json($los->load(['pasar', 'tarif']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Lapak $los)
    {
        $validator = Validator::make($request->all(), [
            'pasar_id' => 'required|exists:pasars,id',
            'tarif_id' => 'required|exists:tarifs,id',
            'nomor' => 'required|string|max:255',
            'nama_blok' => 'required|string|max:255',
            'lokasi' => 'nullable|string|max:255',
            'lebar' => 'required|numeric|min:0',
            'panjang' => 'required|numeric|min:0',
            'status' => 'required|in:tersedia,terisi,diblokir'
        ], [
            'pasar_id.required' => 'Pasar wajib dipilih',
            'pasar_id.exists' => 'Pasar tidak valid',
            'tarif_id.required' => 'Tarif wajib dipilih',
            'tarif_id.exists' => 'Tarif tidak valid',
            'nomor.required' => 'Nomor los wajib diisi',
            'nama_blok.required' => 'Nama blok wajib diisi',
            'lebar.required' => 'Lebar wajib diisi',
            'lebar.numeric' => 'Lebar harus berupa angka',
            'lebar.min' => 'Lebar tidak boleh kurang dari 0',
            'panjang.required' => 'Panjang wajib diisi',
            'panjang.numeric' => 'Panjang harus berupa angka',
            'panjang.min' => 'Panjang tidak boleh kurang dari 0',
            'status.required' => 'Status wajib dipilih',
            'status.in' => 'Status tidak valid'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $los->update([
                'pasar_id' => $request->pasar_id,
                'tarif_id' => $request->tarif_id,
                'nomor' => $request->nomor,
                'nama_blok' => $request->nama_blok,
                'lokasi' => $request->lokasi,
                'lebar' => $request->lebar,
                'panjang' => $request->panjang,
                'status' => $request->status
            ]);

            return response()->json([
                'success' => 'Data los berhasil diperbarui'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat memperbarui data'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Lapak $los)
    {
        try {
            $los->delete();
            return response()->json([
                'success' => 'Data los berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menghapus data'
            ], 500);
        }
    }

    /**
     * Get tarif berdasarkan pasar yang dipilih
     */
    public function getTarifByPasar(Request $request)
    {
        $pasar_id = $request->pasar_id;

        if (!$pasar_id) {
            return response()->json(['tarifs' => []]);
        }

        // Ambil pasar untuk mendapatkan tipe_pasar
        $pasar = Pasar::find($pasar_id);

        if (!$pasar) {
            return response()->json(['tarifs' => []]);
        }

        // Ambil tarif berdasarkan jenis 'Los' dan tipe yang sama dengan tipe_pasar
        $tarifs = Tarif::where('jenis', 'Los')
                      ->where('tipe', $pasar->tipe_pasar)
                      ->get();

        return response()->json(['tarifs' => $tarifs]);
    }
}
