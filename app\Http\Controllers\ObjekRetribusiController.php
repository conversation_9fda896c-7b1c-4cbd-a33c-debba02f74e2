<?php

namespace App\Http\Controllers;

use App\Models\Lapak;
use App\Models\Dagangan;
use Illuminate\Http\Request;
use App\Models\ObjekRetribusi;
use App\Models\WajibRetribusi;
use Illuminate\Support\Facades\Validator;

class ObjekRetribusiController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $objekRetribusi = ObjekRetribusi::with(['wajibRetribusi', 'lapak.pasar', 'lapak'])->latest()->get();
        $wajibRetribusiList = WajibRetribusi::latest()->get();

        $lapakList = Lapak::with('pasar')
            ->where('status', 'tersedia')
            ->latest()
            ->get();

        $daganganList = Dagangan::all();

        return view('pages.retribusi.objek_retribusi.index', compact('objekRetribusi', 'wajibRetribusiList', 'lapakList', 'daganganList'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'wajib_retribusi_id' => 'required|exists:wajib_retribusis,id',
            'lapak_id' => 'required|exists:lapaks,id',
            'dagangan_id' => 'required|exists:dagangans,id',
            'tgl_daftar' => 'required|date',
            'batas_berlaku' => 'required|date|after_or_equal:tgl_daftar',
        ], [
            'wajib_retribusi_id.required' => 'Wajib Retribusi wajib dipilih',
            'wajib_retribusi_id.exists' => 'Wajib Retribusi tidak ditemukan',
            'lapak_id.required' => 'Lapak wajib dipilih',
            'lapak_id.exists' => 'Lapak tidak ditemukan',
            'dagangan_id.required' => 'Dagangan wajib dipilih',
            'dagangan_id.exists' => 'Dagangan tidak ditemukan',
            'tgl_daftar.required' => 'Tanggal daftar wajib diisi',
            'tgl_daftar.date' => 'Tanggal daftar tidak valid',
            'batas_berlaku.required' => 'Batas berlaku wajib diisi',
            'batas_berlaku.date' => 'Batas berlaku tidak valid',
            'batas_berlaku.after_or_equal' => 'Batas berlaku harus setelah atau sama dengan tanggal daftar',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            ObjekRetribusi::create([
                'wajib_retribusi_id' => $request->wajib_retribusi_id,
                'lapak_id' => $request->lapak_id,
                'dagangan_id' => $request->dagangan_id,
                'tgl_daftar' => $request->tgl_daftar,
                'batas_berlaku' => $request->batas_berlaku,
            ]);

            Lapak::where('id', $request->lapak_id)->update(['status' => 'terisi']);

            return response()->json([
                'success' => 'Data objek retribusi berhasil ditambahkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menyimpan data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ObjekRetribusi $objekRetribusi)
    {
        return response()->json($objekRetribusi);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ObjekRetribusi $objekRetribusi)
    {
        $validator = Validator::make($request->all(), [
            'wajib_retribusi_id' => 'required|exists:wajib_retribusis,id',
            'lapak_id' => 'required|exists:lapaks,id',
            'dagangan_id' => 'required|exists:dagangans,id',
            'tgl_daftar' => 'required|date',
            'batas_berlaku' => 'required|date|after_or_equal:tgl_daftar',
        ], [
            'wajib_retribusi_id.required' => 'Wajib Retribusi wajib dipilih',
            'wajib_retribusi_id.exists' => 'Wajib Retribusi tidak ditemukan',
            'lapak_id.required' => 'Lapak wajib dipilih',
            'lapak_id.exists' => 'Lapak tidak ditemukan',
            'dagangan_id.required' => 'Dagangan wajib dipilih',
            'dagangan_id.exists' => 'Dagangan tidak ditemukan',
            'tgl_daftar.required' => 'Tanggal daftar wajib diisi',
            'tgl_daftar.date' => 'Tanggal daftar tidak valid',
            'batas_berlaku.required' => 'Batas berlaku wajib diisi',
            'batas_berlaku.date' => 'Batas berlaku tidak valid',
            'batas_berlaku.after_or_equal' => 'Batas berlaku harus setelah atau sama dengan tanggal daftar',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            if ($objekRetribusi->lapak_id != $request->lapak_id) {
                Lapak::where('id', $objekRetribusi->lapak_id)->update([
                    'status' => 'tersedia'
                ]);

                Lapak::where('id', $request->lapak_id)->update([
                    'status' => 'terisi'
                ]);
            }

            $objekRetribusi->update([
                'wajib_retribusi_id' => $request->wajib_retribusi_id,
                'lapak_id' => $request->lapak_id,
                'dagangan_id' => $request->dagangan_id,
                'tgl_daftar' => $request->tgl_daftar,
                'batas_berlaku' => $request->batas_berlaku,
            ]);

            return response()->json([
                'success' => 'Data objek retribusi berhasil diperbarui'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat memperbarui data',
                'message' => $e->getMessage()
            ], 500);
        }
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ObjekRetribusi $objekRetribusi)
    {
        try {
            Lapak::where('id', $objekRetribusi->lapak_id)->update(['status' => 'tersedia']);

            $objekRetribusi->delete();

            return response()->json([
                'success' => 'Data objek retribusi berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menghapus data',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
