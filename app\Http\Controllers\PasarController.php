<?php

namespace App\Http\Controllers;

use App\Models\Pasar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PasarController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pasars = Pasar::latest()->get();
        return view('pages.pasar.index', compact('pasars'));
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nama_pasar' => 'required|string|max:255|unique:pasars,nama_pasar',
            'tipe_pasar' => 'required|in:A,B,C,D'
        ], [
            'nama_pasar.required' => 'Nama pasar wajib diisi',
            'nama_pasar.unique' => 'Nama pasar sudah ada',
            'tipe_pasar.required' => 'Tipe pasar wajib dipilih',
            'tipe_pasar.in' => 'Tipe pasar tidak valid'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            Pasar::create([
                'nama_pasar' => $request->nama_pasar,
                'tipe_pasar' => $request->tipe_pasar
            ]);

            return response()->json([
                'success' => 'Data pasar berhasil ditambahkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menyimpan data'
            ], 500);
        }
    }


    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Pasar $pasar)
    {
        return response()->json($pasar);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Pasar $pasar)
    {
        $validator = Validator::make($request->all(), [
            'nama_pasar' => 'required|string|max:255|unique:pasars,nama_pasar,' . $pasar->id,
            'tipe_pasar' => 'required|in:A,B,C,D'
        ], [
            'nama_pasar.required' => 'Nama pasar wajib diisi',
            'nama_pasar.unique' => 'Nama pasar sudah ada',
            'tipe_pasar.required' => 'Tipe pasar wajib dipilih',
            'tipe_pasar.in' => 'Tipe pasar tidak valid'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $pasar->update([
                'nama_pasar' => $request->nama_pasar,
                'tipe_pasar' => $request->tipe_pasar
            ]);

            return response()->json([
                'success' => 'Data pasar berhasil diperbarui'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat memperbarui data'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Pasar $pasar)
    {
        try {
            $pasar->delete();
            return response()->json([
                'success' => 'Data pasar berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menghapus data'
            ], 500);
        }
    }
}
