<?php

namespace App\Http\Controllers;

use App\Models\Permohonan;
use App\Models\Lapak;
use App\Models\Dagangan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class PermohonanBaruController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $permohonans = Permohonan::with(['lapak.pasar', 'dagangan'])->baru()->latest()->get();
        $lapaks = Lapak::with('pasar')->where('status', 'tersedia')->get();
        $dagangans = Dagangan::all();

        return view('pages.permohonan.baru', compact('permohonans', 'lapaks', 'dagangans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lapak_id' => 'required|exists:lapaks,id',
            'dagangan_id' => 'required|exists:dagangans,id',
            'nama' => 'required|string|max:255',
            'nik' => 'required|string|max:16',
            'alamat' => 'required|string',
            'no_tlp' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:255',
            'npwrd' => 'nullable|string|max:255'
        ], [
            'lapak_id.required' => 'Lapak wajib dipilih',
            'lapak_id.exists' => 'Lapak tidak valid',
            'dagangan_id.required' => 'Jenis dagangan wajib dipilih',
            'dagangan_id.exists' => 'Jenis dagangan tidak valid',
            'nama.required' => 'Nama wajib diisi',
            'nik.required' => 'NIK wajib diisi',
            'alamat.required' => 'Alamat wajib diisi',
            'email.email' => 'Format email tidak valid'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Generate nama file untuk surat-surat
            $timestamp = now()->format('YmdHis');
            $nik = $request->nik;

            $permohonan = Permohonan::create([
                'lapak_id' => $request->lapak_id,
                'dagangan_id' => $request->dagangan_id,
                'jenis_permohonan' => 'baru',
                'nama' => $request->nama,
                'nik' => $request->nik,
                'alamat' => $request->alamat,
                'no_tlp' => $request->no_tlp,
                'email' => $request->email,
                'npwrd' => $request->npwrd,
                'status' => 'proses',
                // Generate nama file untuk surat-surat (belum ada file fisik)
                'sp_kepala' => "sp_kepala_{$nik}_{$timestamp}.pdf",
                'ba_penunjukan' => "ba_penunjukan_{$nik}_{$timestamp}.pdf",
                'sp_pemilik' => "sp_pemilik_{$nik}_{$timestamp}.pdf",
                'surat_pernyataan' => "surat_pernyataan_{$nik}_{$timestamp}.pdf"
            ]);

            // Update status lapak menjadi terisi
            $lapak = Lapak::find($request->lapak_id);
            $lapak->update(['status' => 'terisi']);

            return response()->json(['success' => 'Permohonan baru berhasil ditambahkan']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Terjadi kesalahan: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Permohonan $permohonanBaru)
    {
        return response()->json($permohonanBaru);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Permohonan $permohonanBaru)
    {
        $validator = Validator::make($request->all(), [
            'lapak_id' => 'required|exists:lapaks,id',
            'dagangan_id' => 'required|exists:dagangans,id',
            'nama' => 'required|string|max:255',
            'nik' => 'required|string|max:16',
            'alamat' => 'required|string',
            'no_tlp' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:255',
            'npwrd' => 'nullable|string|max:255',
            'status' => 'required|in:proses,menunggu_verifikasi,diterima,ditolak',
            'keterangan' => 'nullable|string'
        ], [
            'lapak_id.required' => 'Lapak wajib dipilih',
            'dagangan_id.required' => 'Jenis dagangan wajib dipilih',
            'nama.required' => 'Nama wajib diisi',
            'nik.required' => 'NIK wajib diisi',
            'alamat.required' => 'Alamat wajib diisi',
            'status.required' => 'Status wajib dipilih'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Jika lapak berubah, update status lapak lama dan baru
            if ($permohonanBaru->lapak_id != $request->lapak_id) {
                // Set lapak lama menjadi tersedia
                $lapakLama = Lapak::find($permohonanBaru->lapak_id);
                $lapakLama->update(['status' => 'tersedia']);

                // Set lapak baru menjadi terisi
                $lapakBaru = Lapak::find($request->lapak_id);
                $lapakBaru->update(['status' => 'terisi']);
            }

            $permohonanBaru->update([
                'lapak_id' => $request->lapak_id,
                'dagangan_id' => $request->dagangan_id,
                'nama' => $request->nama,
                'nik' => $request->nik,
                'alamat' => $request->alamat,
                'no_tlp' => $request->no_tlp,
                'email' => $request->email,
                'npwrd' => $request->npwrd,
                'status' => $request->status,
                'keterangan' => $request->keterangan
            ]);

            return response()->json(['success' => 'Permohonan baru berhasil diupdate']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Terjadi kesalahan: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Permohonan $permohonanBaru)
    {
        try {
            // Set lapak menjadi tersedia kembali
            $lapak = Lapak::find($permohonanBaru->lapak_id);
            $lapak->update(['status' => 'tersedia']);

            // Hapus file surat jika ada
            $files = [
                $permohonanBaru->sp_kepala,
                $permohonanBaru->ba_penunjukan,
                $permohonanBaru->sp_pemilik,
                $permohonanBaru->surat_pernyataan,
                $permohonanBaru->ktp,
                $permohonanBaru->pas_foto
            ];

            foreach ($files as $file) {
                if ($file && Storage::disk('public')->exists('surat/' . $file)) {
                    Storage::disk('public')->delete('surat/' . $file);
                }
            }

            $permohonanBaru->delete();

            return response()->json(['success' => 'Permohonan baru berhasil dihapus']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Terjadi kesalahan: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Download surat berdasarkan jenis
     */
    public function downloadSurat(Permohonan $permohonanBaru, $jenis)
    {
        $allowedTypes = ['sp_kepala', 'ba_penunjukan', 'sp_pemilik', 'surat_pernyataan'];

        if (!in_array($jenis, $allowedTypes)) {
            abort(404, 'Jenis surat tidak valid');
        }

        $fileName = $permohonanBaru->$jenis;

        if (!$fileName) {
            abort(404, 'File surat tidak ditemukan');
        }

        // Untuk sementara, generate PDF dummy atau redirect ke generator surat
        // Nanti bisa diganti dengan generator surat yang sebenarnya
        return response()->json([
            'message' => "Download surat {$jenis} untuk {$permohonanBaru->nama}",
            'file' => $fileName,
            'url' => route('permohonan-baru.generate-surat', [$permohonanBaru->id, $jenis])
        ]);
    }

    /**
     * Generate surat (placeholder untuk implementasi nanti)
     */
    public function generateSurat(Permohonan $permohonanBaru, $jenis)
    {
        // Placeholder untuk generate surat
        // Nanti bisa menggunakan library seperti TCPDF, DomPDF, atau template engine
        return response()->json([
            'message' => "Generate surat {$jenis} untuk {$permohonanBaru->nama}",
            'data' => $permohonanBaru
        ]);
    }

    /**
     * Upload file surat yang sudah ditandatangani
     */
    public function uploadSurat(Request $request, Permohonan $permohonanBaru, $jenis)
    {
        $allowedTypes = ['sp_kepala', 'ba_penunjukan', 'sp_pemilik', 'surat_pernyataan', 'ktp', 'pas_foto'];

        if (!in_array($jenis, $allowedTypes)) {
            return response()->json(['error' => 'Jenis file tidak valid'], 400);
        }

        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:pdf,jpg,jpeg,png|max:2048'
        ], [
            'file.required' => 'File wajib dipilih',
            'file.mimes' => 'File harus berformat PDF, JPG, JPEG, atau PNG',
            'file.max' => 'Ukuran file maksimal 2MB'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $file = $request->file('file');
            $originalFileName = $permohonanBaru->$jenis;

            // Hapus file lama jika ada
            if ($originalFileName && Storage::disk('public')->exists('surat/' . $originalFileName)) {
                Storage::disk('public')->delete('surat/' . $originalFileName);
            }

            // Simpan file baru
            $fileName = $file->storeAs('surat', $originalFileName, 'public');

            // Update database
            $permohonanBaru->update([
                $jenis => $originalFileName
            ]);

            return response()->json(['success' => "File {$jenis} berhasil diupload"]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Terjadi kesalahan: ' . $e->getMessage()], 500);
        }
    }
}
