<?php

namespace App\Http\Controllers;

use App\Models\Lapak;
use App\Models\Dagangan;
use App\Models\Permohonan;
use Illuminate\Http\Request;
use App\Models\ObjekRetribusi;
use App\Models\WajibRetribusi;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class PermohonanBaruController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $permohonans = Permohonan::with(['lapak.pasar', 'dagangan'])->baru()->latest()->get();
        $lapaks = Lapak::with('pasar')->where('status', 'tersedia')->get();
        $dagangans = Dagangan::all();

        return view('pages.permohonan.baru', compact('permohonans', 'lapaks', 'dagangans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lapak_id' => 'required|exists:lapaks,id',
            'dagangan_id' => 'required|exists:dagangans,id',
            'nama' => 'required|string|max:255',
            'nik' => 'required|string|max:16',
            'alamat' => 'required|string',
            'no_tlp' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:255',
            'npwrd' => 'nullable|string|max:255'
        ], [
            'lapak_id.required' => 'Lapak wajib dipilih',
            'lapak_id.exists' => 'Lapak tidak valid',
            'dagangan_id.required' => 'Jenis dagangan wajib dipilih',
            'dagangan_id.exists' => 'Jenis dagangan tidak valid',
            'nama.required' => 'Nama wajib diisi',
            'nik.required' => 'NIK wajib diisi',
            'alamat.required' => 'Alamat wajib diisi',
            'email.email' => 'Format email tidak valid'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Generate nama file untuk surat-surat
            $timestamp = now()->format('YmdHis');
            $nik = $request->nik;

            $permohonan = Permohonan::create([
                'lapak_id' => $request->lapak_id,
                'dagangan_id' => $request->dagangan_id,
                'jenis_permohonan' => 'baru',
                'nama' => $request->nama,
                'nik' => $request->nik,
                'alamat' => $request->alamat,
                'no_tlp' => $request->no_tlp,
                'email' => $request->email,
                'npwrd' => $request->npwrd,
                'status' => 'proses',
                // Generate nama file untuk surat-surat (belum ada file fisik)
                'sp_kepala' => "sp_kepala_{$nik}_{$timestamp}.pdf",
                'ba_penunjukan' => "ba_penunjukan_{$nik}_{$timestamp}.pdf",
                'sp_pemilik' => "sp_pemilik_{$nik}_{$timestamp}.pdf",
                'surat_pernyataan' => "surat_pernyataan_{$nik}_{$timestamp}.pdf"
            ]);

            // Update status lapak menjadi terisi
            $lapak = Lapak::find($request->lapak_id);
            $lapak->update(['status' => 'terisi']);

            return response()->json(['success' => 'Permohonan baru berhasil ditambahkan']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Terjadi kesalahan: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Permohonan $permohonanBaru)
    {
        $permohonanBaru->load('lapak', 'lapak.pasar', 'dagangan');

        $permohonanBaru->ba_penunjukan_url = $permohonanBaru->ba_penunjukan ? asset('storage/' . $permohonanBaru->ba_penunjukan) : null;
        $permohonanBaru->sp_pemilik_url = $permohonanBaru->sp_pemilik ? asset('storage/' . $permohonanBaru->sp_pemilik) : null;
        $permohonanBaru->surat_pernyataan_url = $permohonanBaru->surat_pernyataan ? asset('storage/' . $permohonanBaru->surat_pernyataan) : null;
        $permohonanBaru->ktp_url = $permohonanBaru->ktp ? asset('storage/' . $permohonanBaru->ktp) : null;
        $permohonanBaru->pas_foto_url = $permohonanBaru->pas_foto ? asset('storage/' . $permohonanBaru->pas_foto) : null;
        return response()->json($permohonanBaru);
    }



    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Permohonan $permohonanBaru)
    {
        $validator = Validator::make($request->all(), [
            'lapak_id' => 'required|exists:lapaks,id',
            'dagangan_id' => 'required|exists:dagangans,id',
            'nama' => 'required|string|max:255',
            'nik' => 'required|string|max:16',
            'alamat' => 'required|string',
            'no_tlp' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:255',
            'npwrd' => 'nullable|string|max:255',
            'status' => 'required|in:proses,menunggu_verifikasi,diterima,ditolak',
        ], [
            'lapak_id.required' => 'Lapak wajib dipilih',
            'dagangan_id.required' => 'Jenis dagangan wajib dipilih',
            'nama.required' => 'Nama wajib diisi',
            'nik.required' => 'NIK wajib diisi',
            'alamat.required' => 'Alamat wajib diisi',
            'status.required' => 'Status wajib dipilih'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Jika lapak berubah, update status lapak lama dan baru
            if ($permohonanBaru->lapak_id != $request->lapak_id) {
                // Set lapak lama menjadi tersedia
                $lapakLama = Lapak::find($permohonanBaru->lapak_id);
                $lapakLama->update(['status' => 'tersedia']);

                // Set lapak baru menjadi terisi
                $lapakBaru = Lapak::find($request->lapak_id);
                $lapakBaru->update(['status' => 'terisi']);
            }

            $permohonanBaru->update([
                'lapak_id' => $request->lapak_id,
                'dagangan_id' => $request->dagangan_id,
                'nama' => $request->nama,
                'nik' => $request->nik,
                'alamat' => $request->alamat,
                'no_tlp' => $request->no_tlp,
                'email' => $request->email,
                'npwrd' => $request->npwrd,
                'status' => $request->status,
            ]);

            return response()->json(['success' => 'Permohonan baru berhasil diupdate']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Terjadi kesalahan: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Permohonan $permohonanBaru)
    {
        try {
            // Set lapak menjadi tersedia kembali
            $lapak = Lapak::find($permohonanBaru->lapak_id);
            $lapak->update(['status' => 'tersedia']);

            // Hapus file surat jika ada
            $files = [
                $permohonanBaru->ba_penunjukan,
                $permohonanBaru->sp_pemilik,
                $permohonanBaru->surat_pernyataan,
                $permohonanBaru->ktp,
                $permohonanBaru->pas_foto
            ];

            foreach ($files as $file) {
                if ($file && Storage::disk('public')->exists('surat/' . $file)) {
                    Storage::disk('public')->delete('surat/' . $file);
                }
            }

            $permohonanBaru->delete();

            return response()->json(['success' => 'Permohonan baru berhasil dihapus']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Terjadi kesalahan: ' . $e->getMessage()], 500);
        }
    }

    public function uploadDokumen(Request $request, Permohonan $permohonanBaru)
    {
        try {
            $request->validate([
                'ba_penunjukan' => 'nullable|image|max:2048',
                'sp_pemilik' => 'nullable|image|max:2048',
                'surat_pernyataan' => 'nullable|image|max:2048',
                'ktp' => 'nullable|image|max:2048',
                'pas_foto' => 'nullable|image|max:2048',
                'keterangan' => 'nullable|string',
                'persetujuan_kepala_pasar' => 'nullable',
            ]);

            $dokumenFields = ['ba_penunjukan', 'sp_pemilik', 'surat_pernyataan', 'ktp', 'pas_foto'];

            foreach ($dokumenFields as $field) {
                if ($request->hasFile($field)) {
                    if ($permohonanBaru->$field) {
                        Storage::disk('public')->delete($permohonanBaru->$field);
                    }

                    $path = $request->file($field)->store("dokumen_permohonan/{$permohonanBaru->id}", 'public');
                    $permohonanBaru->$field = $path;
                }
            }

            $permohonanBaru->keterangan = $request->input('keterangan');
            $permohonanBaru->persetujuan_kepala_pasar = $request->input('persetujuan_kepala_pasar', 0);

            if ($permohonanBaru->persetujuan_kepala_pasar) {
                $permohonanBaru->status = 'menunggu_verifikasi';
            } else {
                $permohonanBaru->status = 'proses';
            }


            $permohonanBaru->save();

            return response()->json([
                'success' => 'Dokumen berhasil diupload',
                'data' => $permohonanBaru
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function verifikasi(Request $request, Permohonan $permohonanBaru)
    {
        if ($request->status == 'menunggu_tanda_tangan') {
            $permohonanBaru->update(['status' => 'menunggu_tanda_tangan']);
            $permohonanBaru->keterangan = $request->keterangan;
            $permohonanBaru->save();
            return response()->json(['success' => 'Permohonan berhasil disetujui']);
        } elseif ($request->status == 'disetujui') {
            $permohonanBaru->update(['status' => 'diterima']);
            $permohonanBaru->keterangan = $request->keterangan;
            $permohonanBaru->save();

            $wajib = WajibRetribusi::where('npwrd', $permohonanBaru->npwrd)
                ->orWhere('nik', $permohonanBaru->nik)
                ->first();

            if (!$wajib) {
                $wajib = WajibRetribusi::create([
                    'npwrd' => $permohonanBaru->npwrd,
                    'nama' => $permohonanBaru->nama,
                    'nik' => $permohonanBaru->nik,
                    'alamat' => $permohonanBaru->alamat,
                    'no_telp' => $permohonanBaru->no_tlp,
                    'email' => $permohonanBaru->email,
                ]);
            }

            ObjekRetribusi::create([
                'wajib_retribusi_id' => $wajib->id,
                'dagangan_id' => $permohonanBaru->dagangan_id,
                'lapak_id' => $permohonanBaru->lapak_id,
                'tgl_daftar' => $permohonanBaru->created_at->toDateString(),
                'batas_berlaku' => Carbon::now()->addYear(2)->toDateString(),
            ]);
            return response()->json(['success' => 'Permohonan berhasil disetujui']);
        } else {
            $permohonanBaru->update(['status' => 'revisi']);
            $permohonanBaru->keterangan = $request->keterangan;
            $permohonanBaru->save();
            return response()->json(['success' => 'Berhasil minta revisi']);
        }
    }
}
