<?php

namespace App\Http\Controllers;

use App\Models\Permohonan;
use Illuminate\Http\Request;
use App\Models\ObjekRetribusi;

class PermohonanPerpanjangController extends Controller
{
    public function index()
    {   
        $objekRetribusi = ObjekRetribusi::with(['wajibRetribusi','lapak.pasar','lapak'])->where('batas_berlaku', '<', now())->latest()->get();
        $permohonans = Permohonan::with(['lapak.pasar', 'dagangan'])->perpanjang()->latest()->get();
        return view('pages.permohonan.perpanjang', compact('permohonans', 'objekRetribusi'));
    }
}
