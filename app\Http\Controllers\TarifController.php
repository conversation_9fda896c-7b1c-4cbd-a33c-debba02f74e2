<?php

namespace App\Http\Controllers;

use App\Models\Tarif;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TarifController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tarifs = Tarif::latest()->get();
        return view('pages.tarif.index', compact('tarifs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tipe' => 'required|in:A,B,C,D',
            'jenis' => 'required|in:Kios,Los,Selasar',
            'tarif' => 'required|numeric|min:0'
        ], [
            'tipe.required' => 'Tipe tarif wajib dipilih',
            'tipe.in' => 'Tipe tarif tidak valid',
            'jenis.required' => 'Jenis tarif wajib dipilih',
            'jenis.in' => 'Jenis tarif tidak valid',
            'tarif.required' => 'Tarif wajib diisi',
            'tarif.numeric' => 'Tarif harus berupa angka',
            'tarif.min' => 'Tarif tidak boleh kurang dari 0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            Tarif::create([
                'tipe' => $request->tipe,
                'jenis' => $request->jenis,
                'tarif' => $request->tarif
            ]);

            return response()->json([
                'success' => 'Data tarif berhasil ditambahkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menyimpan data'
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Tarif $tarif)
    {
        return response()->json($tarif);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Tarif $tarif)
    {
        $validator = Validator::make($request->all(), [
            'tipe' => 'required|in:A,B,C,D',
            'jenis' => 'required|in:Kios,Los,Selasar',
            'tarif' => 'required|numeric|min:0'
        ], [
            'tipe.required' => 'Tipe tarif wajib dipilih',
            'tipe.in' => 'Tipe tarif tidak valid',
            'jenis.required' => 'Jenis tarif wajib dipilih',
            'jenis.in' => 'Jenis tarif tidak valid',
            'tarif.required' => 'Tarif wajib diisi',
            'tarif.numeric' => 'Tarif harus berupa angka',
            'tarif.min' => 'Tarif tidak boleh kurang dari 0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tarif->update([
                'tipe' => $request->tipe,
                'jenis' => $request->jenis,
                'tarif' => $request->tarif
            ]);

            return response()->json([
                'success' => 'Data tarif berhasil diperbarui'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat memperbarui data'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Tarif $tarif)
    {
        try {
            $tarif->delete();
            return response()->json([
                'success' => 'Data tarif berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menghapus data'
            ], 500);
        }
    }
}
