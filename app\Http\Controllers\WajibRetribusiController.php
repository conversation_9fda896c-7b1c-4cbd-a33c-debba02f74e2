<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ObjekRetribusi;
use App\Models\WajibRetribusi;
use Illuminate\Support\Facades\Validator;

class WajibRetribusiController extends Controller
{
    public function index()
    {
        $wajibRetribusi = WajibRetribusi::latest()->get();

        return view('pages.retribusi.wajib_retribusi.index', compact('wajibRetribusi'));
    }
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'npwrd'     => 'required|string|max:255|unique:wajib_retribusis,npwrd',
            'nama'      => 'required|string|max:255',
            'nik'       => 'required|string|max:16|unique:wajib_retribusis,nik',
            'alamat'    => 'required|string',
            'no_telp'   => 'nullable|string|max:20',
            'email'     => 'nullable|email|max:255|unique:wajib_retribusis,email',
        ], [
            'npwrd.required'    => 'NPWRD wajib diisi',
            'npwrd.unique'      => 'NPWRD sudah digunakan',
            'nama.required'     => 'Nama wajib diisi',
            'nik.required'      => 'NIK wajib diisi',
            'nik.unique'        => 'NIK sudah digunakan',
            'nik.max'           => 'NIK maksimal 16 karakter',
            'email.email'       => 'Format email tidak valid',
            'email.unique'      => 'Email sudah digunakan',
            'email.max'         => 'Email terlalu panjang',
            'no_telp.max'       => 'Nomor telepon terlalu panjang',
            'alamat.required'   => 'Alamat wajib diisi',

        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            WajibRetribusi::create($request->all());

            return response()->json([
                'success' => 'Data wajib retribusi berhasil ditambahkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menyimpan data'
            ], 500);
        }
    }


    public function edit(WajibRetribusi $wajibRetribusi)
    {
        return response()->json($wajibRetribusi);
    }

    public function update(Request $request, WajibRetribusi $wajibRetribusi)
    {
        $validator = Validator::make($request->all(), [
            'npwrd'     => 'required|string|max:255|unique:wajib_retribusis,npwrd,' . $wajibRetribusi->id,
            'nama'      => 'required|string|max:255',
            'nik'       => 'required|string|max:16|unique:wajib_retribusis,nik,' . $wajibRetribusi->id,
            'alamat'    => 'required|string',
            'no_telp'   => 'nullable|string|max:20',
            'email'     => 'nullable|email|max:255|unique:wajib_retribusis,email,' . $wajibRetribusi->id
        ], [
            'npwrd.required'    => 'NPWRD wajib diisi',
            'nama.required'     => 'Nama wajib diisi',
            'nik.required'      => 'NIK wajib diisi',
            'nik.max'           => 'NIK maksimal 16 karakter',
            'email.email'       => 'Format email tidak valid',
            'email.max'         => 'Email terlalu panjang',
            'no_telp.max'       => 'Nomor telepon terlalu panjang',
            'email.unique' => 'Email sudah digunakan',
            'nik.unique'   => 'NIK sudah digunakan',
            'npwrd.unique' => 'NPWRD sudah digunakan',
            'alamat.required'   => 'Alamat wajib diisi',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $wajibRetribusi->update([
                'npwrd'     => $request->npwrd,
                'nama'      => $request->nama,
                'nik'       => $request->nik,
                'alamat'    => $request->alamat,
                'no_telp'   => $request->no_telp,
                'email'     => $request->email
            ]);

            return response()->json([
                'success' => 'Data wajib retribusi berhasil diperbarui'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat memperbarui data'
            ], 500);
        }
    }

    public function destroy(WajibRetribusi $wajibRetribusi)
    {
        try {
            $objekRetribusis = ObjekRetribusi::where('wajib_retribusi_id', $wajibRetribusi->id)->get();

            foreach ($objekRetribusis as $objek) {
                $lapak = $objek->lapak;
                if ($lapak) {
                    $lapak->update(['status' => 'tersedia']);
                }
            }

            $wajibRetribusi->delete();

            return response()->json([
                'success' => 'Data wajib retribusi berhasil dihapus dan status lapak telah diperbarui.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Terjadi kesalahan saat menghapus data: ' . $e->getMessage()
            ], 500);
        }
    }
}
