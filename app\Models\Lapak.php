<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Lapak extends Model
{
    protected $guarded = ['id'];

    protected $casts = [
        'lebar' => 'float',
        'panjang' => 'float'
    ];

    // <PERSON>lasi ke Pasar
    public function pasar()
    {
        return $this->belongsTo(Pasar::class);
    }

    // Relasi ke Tarif
    public function tarif()
    {
        return $this->belongsTo(Tarif::class);
    }

    // Scope untuk filter berdasarkan jenis
    public function scopeKios($query)
    {
        return $query->where('jenis', 'kios');
    }

    public function scopeLos($query)
    {
        return $query->where('jenis', 'los');
    }

    public function permohonan()
    {
        return $this->hasMany(Permohonan::class);
    }

    public function objekRetribusi()
    {
        return $this->hasOne(ObjekRetribusi::class);
    }
}
