<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Pasar extends Model
{
    protected $guarded = ['id'];

    public function user()
    {
        return $this->hasOne(User::class);
    }

    public function kepalaPasar()
    {
        return $this->hasOne(User::class)->where('role', 'kepala_pasar');
    }

    public function lapak()
    {
        return $this->hasMany(Lapak::class);
    }
}
