<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Permohonan extends Model
{
    protected $guarded = ['id'];

    public function lapak()
    {
        return $this->belongsTo(Lapak::class);
    }

    public function dagangan()
    {
        return $this->belongsTo(Dagangan::class);
    }

    public function scopeBaru($query)
    {
        return $query->where('jenis_permohonan', 'baru');
    }

    public function scopePerpanjang($query)
    {
        return $query->where('jenis_permohonan', 'perpanjangan');
    }
}
