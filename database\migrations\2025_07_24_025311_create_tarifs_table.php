<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tarifs', function (Blueprint $table) {
            $table->id();
            $table->enum('tipe', ['A', 'B', 'C', 'D']);
            $table->enum('jenis', ['Ki<PERSON>', 'Los', '<PERSON>las<PERSON>']);
            $table->string('letak')->nullable();
            $table->unsignedInteger('tarif');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tarifs');
    }
};
