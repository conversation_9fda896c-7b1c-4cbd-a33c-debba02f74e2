<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lapaks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pasar_id')->nullable()->index()->references('id')->on('pasars')->onDelete('cascade');
            $table->foreignId('tarif_id')->nullable()->index()->references('id')->on('tarifs')->onDelete('cascade');
            $table->enum('status', ['tersedia', 'terisi', 'diblokir'])->default('tersedia');
            $table->enum('jenis', ['kios', 'los']);
            $table->string('nomor');
            $table->string('nama_blok');
            $table->double('lebar');
            $table->double('panjang');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lapaks');
    }
};
