<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permohonans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lapak_id')->references('id')->on('lapaks')->onDelete('cascade');
            $table->foreignId('dagangan_id')->references('id')->on('dagangans')->onDelete('cascade');
            $table->enum('jenis_permohonan',['baru','perpanjangan']);
            $table->string('nama');
            $table->string('nik');
            $table->string('alamat');
            $table->string('no_tlp')->nullable();
            $table->string('email')->nullable();
            $table->string('ba_penunjukan')->nullable();
            $table->string('sp_pemilik')->nullable();
            $table->string('surat_pernyataan')->nullable();
            $table->string('ktp')->nullable();
            $table->string('pas_foto')->nullable();
            $table->string('npwrd')->nullable();
            $table->enum('status',['proses','menunggu_verifikasi','diterima','ditolak'])->default('proses');
            $table->boolean('persetujuan_kepala_pasar')->default(false);
            $table->boolean('disetujui_kepala_pasar')->default(false);
            $table->text('keterangan')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permohonans');
    }
};
