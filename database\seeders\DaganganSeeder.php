<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Dagangan;

class DaganganSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $dagangans = [
            ['nama_dagangan' => 'Sayuran Segar'],
            ['nama_dagangan' => 'Buah-buahan'],
            ['nama_dagangan' => 'Daging dan Ikan'],
            ['nama_dagangan' => '<PERSON><PERSON><PERSON>'],
            ['nama_dagangan' => 'Pakaian'],
            ['nama_dagangan' => 'Elektronik']
        ];

        foreach ($dagangans as $dagangan) {
            Dagangan::create($dagangan);
        }

        $this->command->info('Data sample Dagangan berhasil ditambahkan.');
    }
}
