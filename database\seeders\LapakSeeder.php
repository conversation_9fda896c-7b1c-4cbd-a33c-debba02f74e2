<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Lapak;
use App\Models\Pasar;
use App\Models\Tarif;

class LapakSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ambil data pasar dan tarif yang sudah ada
        $pasar = Pasar::first();
        $tarifKios = Tarif::where('jenis', 'Kios')->first();
        $tarifLos = Tarif::where('jenis', 'Los')->first();

        if (!$pasar || !$tarifKios || !$tarifLos) {
            $this->command->info('Pastikan data Pasar dan Tarif sudah ada sebelum menjalankan seeder ini.');
            return;
        }

        // Data sample untuk Kios
        $kiosData = [
            [
                'pasar_id' => $pasar->id,
                'tarif_id' => $tarifKios->id,
                'jenis' => 'kios',
                'nomor' => 'K001',
                'nama_blok' => 'Blok A',
                'lokasi' => 'Lantai 1 Sektor Utara',
                'lebar' => 3.0,
                'panjang' => 4.0,
                'status' => 'tersedia'
            ],
            [
                'pasar_id' => $pasar->id,
                'tarif_id' => $tarifKios->id,
                'jenis' => 'kios',
                'nomor' => 'K002',
                'nama_blok' => 'Blok A',
                'lokasi' => 'Lantai 1 Sektor Utara',
                'lebar' => 3.0,
                'panjang' => 4.0,
                'status' => 'terisi'
            ],
            [
                'pasar_id' => $pasar->id,
                'tarif_id' => $tarifKios->id,
                'jenis' => 'kios',
                'nomor' => 'K003',
                'nama_blok' => 'Blok B',
                'lokasi' => 'Lantai 1 Sektor Selatan',
                'lebar' => 2.5,
                'panjang' => 3.5,
                'status' => 'tersedia'
            ],
            [
                'pasar_id' => $pasar->id,
                'tarif_id' => $tarifKios->id,
                'jenis' => 'kios',
                'nomor' => 'K004',
                'nama_blok' => 'Blok B',
                'lokasi' => 'Lantai 1 Sektor Selatan',
                'lebar' => 2.5,
                'panjang' => 3.5,
                'status' => 'diblokir'
            ]
        ];

        // Data sample untuk Los
        $losData = [
            [
                'pasar_id' => $pasar->id,
                'tarif_id' => $tarifLos->id,
                'jenis' => 'los',
                'nomor' => 'L001',
                'nama_blok' => 'Blok C',
                'lokasi' => 'Area Terbuka Timur',
                'lebar' => 2.0,
                'panjang' => 3.0,
                'status' => 'tersedia'
            ],
            [
                'pasar_id' => $pasar->id,
                'tarif_id' => $tarifLos->id,
                'jenis' => 'los',
                'nomor' => 'L002',
                'nama_blok' => 'Blok C',
                'lokasi' => 'Area Terbuka Timur',
                'lebar' => 2.0,
                'panjang' => 3.0,
                'status' => 'terisi'
            ],
            [
                'pasar_id' => $pasar->id,
                'tarif_id' => $tarifLos->id,
                'jenis' => 'los',
                'nomor' => 'L003',
                'nama_blok' => 'Blok D',
                'lokasi' => 'Area Terbuka Barat',
                'lebar' => 1.5,
                'panjang' => 2.5,
                'status' => 'tersedia'
            ],
            [
                'pasar_id' => $pasar->id,
                'tarif_id' => $tarifLos->id,
                'jenis' => 'los',
                'nomor' => 'L004',
                'nama_blok' => 'Blok D',
                'lokasi' => 'Area Terbuka Barat',
                'lebar' => 1.5,
                'panjang' => 2.5,
                'status' => 'tersedia'
            ]
        ];

        // Insert data kios
        foreach ($kiosData as $kios) {
            Lapak::create($kios);
        }

        // Insert data los
        foreach ($losData as $los) {
            Lapak::create($los);
        }

        $this->command->info('Data sample Kios dan Los berhasil ditambahkan.');
    }
}
