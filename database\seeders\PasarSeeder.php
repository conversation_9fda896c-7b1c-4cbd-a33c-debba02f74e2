<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Pasar;

class PasarSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pasars = [
            [
                'nama_pasar' => 'Pasar Sentral',
                'tipe_pasar' => 'Modern'
            ],
            [
                'nama_pasar' => 'Pasar Tradisional',
                'tipe_pasar' => 'Tradisional'
            ]
        ];

        foreach ($pasars as $pasar) {
            Pasar::create($pasar);
        }
    }
}
