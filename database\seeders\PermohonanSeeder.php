<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Permohonan;
use App\Models\Lapak;
use App\Models\Dagangan;

class PermohonanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ambil data lapak dan dagangan yang sudah ada
        $lapaks = Lapak::take(3)->get(); // Ambil lapak apapun statusnya
        $dagangans = Dagangan::take(3)->get();

        if ($lapaks->count() < 3 || $dagangans->count() < 3) {
            $this->command->info('Pastikan data Lapak dan Dagangan sudah ada sebelum menjalankan seeder ini.');
            return;
        }

        // Data sample untuk Permohonan Baru
        $permohonans = [
            [
                'lapak_id' => $lapaks[0]->id,
                'dagangan_id' => $dagangans[0]->id,
                'jenis_permohonan' => 'baru',
                'nama' => '<PERSON>',
                'nik' => '3201234567890123',
                'alamat' => 'Jl. Merdeka No. 123, Jakarta Pusat',
                'no_tlp' => '081234567890',
                'email' => '<EMAIL>',
                'npwrd' => 'P.2.02.0000001.14.01',
                'status' => 'proses',
                'sp_kepala' => 'sp_kepala_3201234567890123_' . now()->format('YmdHis') . '.pdf',
                'ba_penunjukan' => 'ba_penunjukan_3201234567890123_' . now()->format('YmdHis') . '.pdf',
                'sp_pemilik' => 'sp_pemilik_3201234567890123_' . now()->format('YmdHis') . '.pdf',
                'surat_pernyataan' => 'surat_pernyataan_3201234567890123_' . now()->format('YmdHis') . '.pdf'
            ],
            [
                'lapak_id' => $lapaks[1]->id,
                'dagangan_id' => $dagangans[1]->id,
                'jenis_permohonan' => 'baru',
                'nama' => 'Siti Nurhaliza',
                'nik' => '3201234567890124',
                'alamat' => 'Jl. Sudirman No. 456, Jakarta Selatan',
                'no_tlp' => '081234567891',
                'email' => '<EMAIL>',
                'npwrd' => 'P.2.02.0000002.14.01',
                'status' => 'menunggu_verifikasi',
                'sp_kepala' => 'sp_kepala_3201234567890124_' . now()->format('YmdHis') . '.pdf',
                'ba_penunjukan' => 'ba_penunjukan_3201234567890124_' . now()->format('YmdHis') . '.pdf',
                'sp_pemilik' => 'sp_pemilik_3201234567890124_' . now()->format('YmdHis') . '.pdf',
                'surat_pernyataan' => 'surat_pernyataan_3201234567890124_' . now()->format('YmdHis') . '.pdf'
            ],
            [
                'lapak_id' => $lapaks[2]->id,
                'dagangan_id' => $dagangans[2]->id,
                'jenis_permohonan' => 'baru',
                'nama' => 'Budi Santoso',
                'nik' => '3201234567890125',
                'alamat' => 'Jl. Thamrin No. 789, Jakarta Pusat',
                'no_tlp' => '081234567892',
                'email' => '<EMAIL>',
                'status' => 'diterima',
                'sp_kepala' => 'sp_kepala_3201234567890125_' . now()->format('YmdHis') . '.pdf',
                'ba_penunjukan' => 'ba_penunjukan_3201234567890125_' . now()->format('YmdHis') . '.pdf',
                'sp_pemilik' => 'sp_pemilik_3201234567890125_' . now()->format('YmdHis') . '.pdf',
                'surat_pernyataan' => 'surat_pernyataan_3201234567890125_' . now()->format('YmdHis') . '.pdf'
            ]
        ];

        // Insert data permohonan
        foreach ($permohonans as $permohonan) {
            Permohonan::create($permohonan);

            // Update status lapak menjadi terisi
            $lapak = Lapak::find($permohonan['lapak_id']);
            $lapak->update(['status' => 'terisi']);
        }

        $this->command->info('Data sample Permohonan Baru berhasil ditambahkan.');
    }
}
