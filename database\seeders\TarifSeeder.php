<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Tarif;

class TarifSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tarifs = [
            [
                'tipe' => 'A',
                'jenis' => 'Kios',
                'letak' => 'Lantai 1',
                'tarif' => 50000,
            ],
            [
                'tipe' => 'A',
                'jenis' => 'Los',
                'letak' => 'Lantai 1',
                'tarif' => 30000,
            ],
            [
                'tipe' => 'A',
                'jenis' => 'Selasar',
                'letak' => 'Lantai 1',
                'tarif' => 20000,
            ],
            [
                'tipe' => 'B',
                'jenis' => 'Kios',
                'letak' => 'Lantai 2',
                'tarif' => 40000,
            ],
            [
                'tipe' => 'B',
                'jenis' => 'Los',
                'letak' => 'Lantai 2',
                'tarif' => 25000,
            ],
            [
                'tipe' => 'C',
                'jenis' => 'Kios',
                'letak' => 'Basement',
                'tarif' => 35000,
            ],
            [
                'tipe' => 'C',
                'jenis' => 'Selasar',
                'letak' => 'Basement',
                'tarif' => 15000,
            ],
            [
                'tipe' => 'D',
                'jenis' => 'Los',
                'letak' => 'Outdoor',
                'tarif' => 20000,
            ],
        ];

        foreach ($tarifs as $tarif) {
            Tarif::create($tarif);
        }
    }
}
