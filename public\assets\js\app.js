

$(function() {
	"use strict";

  // Tooltops

    $(function () {
        $('[data-bs-toggle="tooltip"]').tooltip();
    })



    $(".nav-toggle-icon").on("click", function() {
		$(".wrapper").toggleClass("toggled")
	})

    $(".mobile-toggle-icon").on("click", function() {
		$(".wrapper").addClass("toggled")
	})

	$(function() {
		for (var e = window.location, o = $(".metismenu li a").filter(function() {
				return this.href == e
			}).addClass("").parent().addClass("mm-active"); o.is("li");) o = o.parent("").addClass("mm-show").parent("").addClass("mm-active")
	})


	$(".toggle-icon").click(function() {
		$(".wrapper").hasClass("toggled") ? ($(".wrapper").removeClass("toggled"), $(".sidebar-wrapper").unbind("hover")) : ($(".wrapper").addClass("toggled"), $(".sidebar-wrapper").hover(function() {
			$(".wrapper").addClass("sidebar-hovered")
		}, function() {
			$(".wrapper").removeClass("sidebar-hovered")
		}))
	})



	$(function() {
		$("#menu").metisMenu()
	})


	$(".search-toggle-icon").on("click", function() {
		$(".top-header .navbar form").addClass("full-searchbar")
	})
	$(".search-close-icon").on("click", function() {
		$(".top-header .navbar form").removeClass("full-searchbar")
	})


	$(".chat-toggle-btn").on("click", function() {
		$(".chat-wrapper").toggleClass("chat-toggled")
	}), $(".chat-toggle-btn-mobile").on("click", function() {
		$(".chat-wrapper").removeClass("chat-toggled")
	}), $(".email-toggle-btn").on("click", function() {
		$(".email-wrapper").toggleClass("email-toggled")
	}), $(".email-toggle-btn-mobile").on("click", function() {
		$(".email-wrapper").removeClass("email-toggled")
	}), $(".compose-mail-btn").on("click", function() {
		$(".compose-mail-popup").show()
	}), $(".compose-mail-close").on("click", function() {
		$(".compose-mail-popup").hide()
	})


	$(document).ready(function() {
		$(window).on("scroll", function() {
			$(this).scrollTop() > 300 ? $(".back-to-top").fadeIn() : $(".back-to-top").fadeOut()
		}), $(".back-to-top").on("click", function() {
			return $("html, body").animate({
				scrollTop: 0
			}, 600), !1
		})
	})


	// switcher with localStorage support

	// Load saved theme on page load
	function loadSavedTheme() {
		const savedTheme = localStorage.getItem('selectedTheme');
		const savedHeaderColor = localStorage.getItem('selectedHeaderColor');

		if (savedTheme) {
			setTheme(savedTheme);
		} else {
			setTheme("light-theme");
		}

		if (savedHeaderColor) {
			setHeaderColor(savedHeaderColor);
		} else {
			// Set default header color if none saved
			setHeaderColor("headercolor1");
		}
	}

	// Set theme function
	function setTheme(theme) {
		// Remove existing theme classes
		$("html").removeClass("light-theme dark-theme semi-dark minimal-theme");
		// Add new theme class
		$("html").addClass(theme);

		// Update radio button selection
		$('input[name="inlineRadioOptions"]').prop('checked', false);
		if (theme === "light-theme") {
			$("#LightTheme").prop('checked', true);
		} else if (theme === "dark-theme") {
			$("#DarkTheme").prop('checked', true);
		} else if (theme === "semi-dark") {
			$("#SemiDarkTheme").prop('checked', true);
		} else if (theme === "minimal-theme") {
			$("#MinimalTheme").prop('checked', true);
		}
	}

	// Set header color function
	function setHeaderColor(color) {
		$("html").removeClass("headercolor0 headercolor1 headercolor2 headercolor3 headercolor4 headercolor5 headercolor6 headercolor7 headercolor8");
		if (color !== "default") {
			$("html").addClass("color-header " + color);
		}

		// Update visual indicator
		$('.header-colors-indigators .indigator').removeClass('selected');
		if (color !== "default") {
			$('#' + color).addClass('selected');
		}
	}

	// Theme click handlers
	$("#LightTheme").on("click", function() {
		setTheme("light-theme");
		localStorage.setItem('selectedTheme', 'light-theme');
	});

	$("#DarkTheme").on("click", function() {
		setTheme("dark-theme");
		localStorage.setItem('selectedTheme', 'dark-theme');
	});

	$("#SemiDarkTheme").on("click", function() {
		setTheme("semi-dark");
		localStorage.setItem('selectedTheme', 'semi-dark');
	});

	$("#MinimalTheme").on("click", function() {
		setTheme("minimal-theme");
		localStorage.setItem('selectedTheme', 'minimal-theme');
	});

	// Load theme on page ready
	loadSavedTheme();


	// Header color click handlers
	$("#headercolor0").on("click", function() {
		setHeaderColor("headercolor0");
		localStorage.setItem('selectedHeaderColor', 'headercolor0');
	});

	$("#headercolor1").on("click", function() {
		setHeaderColor("headercolor1");
		localStorage.setItem('selectedHeaderColor', 'headercolor1');
	});

	$("#headercolor2").on("click", function() {
		setHeaderColor("headercolor2");
		localStorage.setItem('selectedHeaderColor', 'headercolor2');
	});

	$("#headercolor3").on("click", function() {
		setHeaderColor("headercolor3");
		localStorage.setItem('selectedHeaderColor', 'headercolor3');
	});

	$("#headercolor4").on("click", function() {
		setHeaderColor("headercolor4");
		localStorage.setItem('selectedHeaderColor', 'headercolor4');
	});

	$("#headercolor5").on("click", function() {
		setHeaderColor("headercolor5");
		localStorage.setItem('selectedHeaderColor', 'headercolor5');
	});

	$("#headercolor6").on("click", function() {
		setHeaderColor("headercolor6");
		localStorage.setItem('selectedHeaderColor', 'headercolor6');
	});

	$("#headercolor7").on("click", function() {
		setHeaderColor("headercolor7");
		localStorage.setItem('selectedHeaderColor', 'headercolor7');
	});

	$("#headercolor8").on("click", function() {
		setHeaderColor("headercolor8");
		localStorage.setItem('selectedHeaderColor', 'headercolor8');
	});


	// new PerfectScrollbar(".header-message-list")
    new PerfectScrollbar(".header-notifications-list")



});