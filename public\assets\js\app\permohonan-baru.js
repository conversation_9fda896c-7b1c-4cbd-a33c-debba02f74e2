FilePond.registerPlugin(
    FilePondPluginImagePreview,
    FilePondPluginFileValidateSize,
    FilePondPluginFileValidateType
);
initializeFilePond();

function initializeFilePond() {
    const filePondElements = document.querySelectorAll(".filepond");

    filePondElements.forEach(function (element) {
        const pond = FilePond.create(element, {
            server: null,
            storeAsFile: true,
            instantUpload: false,
            allowMultiple: false,
            maxFiles: 1,
            maxFileSize: "2MB",
            acceptedFileTypes: ["image/*"],
            labelIdle:
                'Drag & Drop file atau <span class="filepond--label-action">Browse</span>',
            labelFileWaitingForSize: "Menunggu ukuran file",
            labelFileSizeNotAvailable: "Ukuran file tidak tersedia",
            labelFileLoading: "Loading",
            labelFileLoadError: "Error saat loading",
            labelFileProcessing: "Uploading",
            labelFileProcessingComplete: "Upload selesai",
            labelFileProcessingAborted: "Upload dibatalkan",
            labelFileProcessingError: "Error saat upload",
            labelFileProcessingRevertError: "Error saat revert",
            labelFileRemoveError: "Error saat hapus",
            labelTapToCancel: "tap untuk batal",
            labelTapToRetry: "tap untuk coba lagi",
            labelTapToUndo: "tap untuk undo",
            labelButtonRemoveItem: "Hapus",
            labelButtonAbortItemLoad: "Batal",
            labelButtonRetryItemLoad: "Coba lagi",
            labelButtonAbortItemProcessing: "Batal",
            labelButtonUndoItemProcessing: "Undo",
            labelButtonRetryItemProcessing: "Coba lagi",
            labelButtonProcessItem: "Upload",
            labelMaxFileSizeExceeded: "File terlalu besar",
            labelMaxFileSize: "Ukuran file maksimal {filesize}",
            labelMaxTotalFileSizeExceeded: "Total ukuran file terlalu besar",
            labelMaxTotalFileSize: "Total ukuran file maksimal {filesize}",
            labelFileTypeNotAllowed: "Jenis file tidak diizinkan",
            fileValidateTypeLabelExpectedTypes:
                "Expects {allButLastType} or {lastType}",
            imagePreviewHeight: 150,
            stylePanelLayout: "compact",
            styleLoadIndicatorPosition: "center bottom",
            styleProgressIndicatorPosition: "right bottom",
            styleButtonRemoveItemPosition: "left bottom",
            styleButtonProcessItemPosition: "right bottom",
        });

        element.filePond = pond;
    });
}
