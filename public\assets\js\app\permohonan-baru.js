  // Status file masing-masing field
    let filePondStatus = {
        ba_penunjukan: false,
        sp_pemilik: false,
        surat_pernyataan: false,
        ktp: false,
        pas_foto: false
    };

    // Cek apakah file ada (preview dari DB atau input FilePond)
    function hasFile(fieldName) {
        const preview = document.getElementById(`preview_${fieldName}`);
        const fromDatabase = preview && preview.classList.contains('terisi');
        const fromFilePond = filePondStatus[fieldName];
        return fromDatabase || fromFilePond;
    }

    // Update status tiap FilePond
    function updateFilePondStatus(fieldName, hasFile) {
        filePondStatus[fieldName] = hasFile;
        updateCheckbox();
    }

    // Cek semua file sudah diupload
    function allFilesReady() {
        return Object.keys(filePondStatus).every(field => hasFile(field));
    }

    // Update checkbox berdasarkan status upload
    function updateCheckbox() {
        const checkbox = document.getElementById('persetujuanKepalaPasar');
        if (!checkbox) return;

        if (allFilesReady()) {
            checkbox.disabled = false;
        } else {
            checkbox.disabled = true;
            checkbox.checked = false;
        }
    }

    // Saat checkbox diklik (jika belum lengkap, batalkan centang)
    document.addEventListener("DOMContentLoaded", function () {
        const checkbox = document.getElementById('persetujuanKepalaPasar');
        if (checkbox) {
            checkbox.addEventListener('click', function (e) {
                if (!allFilesReady()) {
                    e.preventDefault();
                    alert('Semua dokumen harus dipilih terlebih dahulu!');
                }
            });
        }

        // Saat form disubmit (minimal satu file harus ada)
        const form = document.getElementById('uploadForm');
        if (form) {
            form.addEventListener('submit', function (e) {
                const hasAnyFile = Object.keys(filePondStatus).some(field => hasFile(field));
                if (!hasAnyFile) {
                    e.preventDefault();
                    alert('Pilih minimal satu dokumen!');
                }
            });
        }

        // Inisialisasi FilePond setelah DOM siap
        FilePond.registerPlugin(
            FilePondPluginImagePreview,
            FilePondPluginFileValidateSize,
            FilePondPluginFileValidateType
        );
    });

    function initializeFilePond() {
        const filePondElements = document.querySelectorAll(".filepond");

        filePondElements.forEach(function (element) {
            const fieldName = element.id;

            const pond = FilePond.create(element, {
                server: null,
                storeAsFile: true,
                instantUpload: false,
                allowMultiple: false,
                maxFiles: 1,
                maxFileSize: "2MB",
                acceptedFileTypes: ["image/*"],
                labelIdle: 'Drag & Drop file atau <span class="filepond--label-action">Browse</span>',
                labelFileWaitingForSize: "Menunggu ukuran file",
                labelFileSizeNotAvailable: "Ukuran file tidak tersedia",
                labelFileLoading: "Loading",
                labelFileLoadError: "Error saat loading",
                labelFileProcessing: "Uploading",
                labelFileProcessingComplete: "Upload selesai",
                labelFileProcessingAborted: "Upload dibatalkan",
                labelFileProcessingError: "Error saat upload",
                labelFileProcessingRevertError: "Error saat revert",
                labelFileRemoveError: "Error saat hapus",
                labelTapToCancel: "tap untuk batal",
                labelTapToRetry: "tap untuk coba lagi",
                labelTapToUndo: "tap untuk undo",
                labelButtonRemoveItem: "Hapus",
                labelButtonAbortItemLoad: "Batal",
                labelButtonRetryItemLoad: "Coba lagi",
                labelButtonAbortItemProcessing: "Batal",
                labelButtonUndoItemProcessing: "Undo",
                labelButtonRetryItemProcessing: "Coba lagi",
                labelButtonProcessItem: "Upload",
                labelMaxFileSizeExceeded: "File terlalu besar",
                labelMaxFileSize: "Ukuran file maksimal {filesize}",
                labelMaxTotalFileSizeExceeded: "Total ukuran file terlalu besar",
                labelMaxTotalFileSize: "Total ukuran file maksimal {filesize}",
                labelFileTypeNotAllowed: "Jenis file tidak diizinkan",
                fileValidateTypeLabelExpectedTypes: "Expects {allButLastType} or {lastType}",
                imagePreviewHeight: 150,
                stylePanelLayout: "compact",
                styleLoadIndicatorPosition: "center bottom",
                styleProgressIndicatorPosition: "right bottom",
                styleButtonRemoveItemPosition: "left bottom",
                styleButtonProcessItemPosition: "right bottom",
            });

            pond.on("addfile", () => updateFilePondStatus(fieldName, true));
            pond.on("removefile", () => updateFilePondStatus(fieldName, false));

            element.filePond = pond;
        });
    }

    // Bisa dipanggil dari luar saat modal dibuka
    function initModal() {
        updateCheckbox();
    }