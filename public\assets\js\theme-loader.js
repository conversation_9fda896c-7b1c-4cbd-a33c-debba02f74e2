/**
 * Theme Loader - Load saved theme immediately to prevent flash
 * This script must be loaded in <head> before CSS to prevent theme flash
 */
(function() {
    'use strict';
    
    // Load saved theme from localStorage
    const savedTheme = localStorage.getItem('selectedTheme');
    const savedHeaderColor = localStorage.getItem('selectedHeaderColor');
    const html = document.documentElement;
    
    // Apply saved theme immediately
    if (savedTheme) {
        // Remove all theme classes first
        html.classList.remove('light-theme', 'dark-theme', 'semi-dark', 'minimal-theme');
        
        // Add the saved theme class
        switch(savedTheme) {
            case 'dark-theme':
                html.classList.add('dark-theme');
                break;
            case 'semi-dark':
                html.classList.add('semi-dark');
                break;
            case 'minimal-theme':
                html.classList.add('minimal-theme');
                break;
            default:
                html.classList.add('light-theme');
        }
    }
    
    // Apply saved header color immediately
    if (savedHeaderColor) {
        // Remove all header color classes
        html.classList.remove(
            'headercolor0', 'headercolor1', 'headercolor2', 'headercolor3', 
            'headercolor4', 'headercolor5', 'headercolor6', 'headercolor7', 'headercolor8'
        );
        
        // Add the saved header color class
        html.classList.add(savedHeaderColor);
    }
})();
