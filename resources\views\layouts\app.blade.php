<!doctype html>
<html lang="en" class="light-theme">
@include('partials.head')
@stack('styles')

<style>
/* Fix mobile toggle icon visibility in dark theme */
[data-bs-theme="dark"] .mobile-toggle-icon,
[data-bs-theme="dark"] .mobile-toggle-icon i {
    color: #ffffff !important;
}

/* Light theme - ensure icon is visible */
[data-bs-theme="light"] .mobile-toggle-icon,
[data-bs-theme="light"] .mobile-toggle-icon i {
    color: #293445 !important;
}

/* Default theme */
.mobile-toggle-icon,
.mobile-toggle-icon i {
    color: #293445 !important;
    transition: color 0.3s ease;
}

/* Hover effect */
.mobile-toggle-icon:hover,
.mobile-toggle-icon:hover i {
    opacity: 0.8;
}

/* Ensure visibility on all header color variations */
.header-color1 .mobile-toggle-icon,
.header-color1 .mobile-toggle-icon i {
    color: #ffffff !important;
}

.header-color2 .mobile-toggle-icon,
.header-color2 .mobile-toggle-icon i {
    color: #ffffff !important;
}

.header-color3 .mobile-toggle-icon,
.header-color3 .mobile-toggle-icon i {
    color: #ffffff !important;
}

.header-color4 .mobile-toggle-icon,
.header-color4 .mobile-toggle-icon i {
    color: #ffffff !important;
}

.header-color5 .mobile-toggle-icon,
.header-color5 .mobile-toggle-icon i {
    color: #ffffff !important;
}

.header-color6 .mobile-toggle-icon,
.header-color6 .mobile-toggle-icon i {
    color: #ffffff !important;
}

.header-color7 .mobile-toggle-icon,
.header-color7 .mobile-toggle-icon i {
    color: #ffffff !important;
}

.header-color8 .mobile-toggle-icon,
.header-color8 .mobile-toggle-icon i {
    color: #ffffff !important;
}
</style>

<!-- Load saved theme before body renders to prevent flash -->
<script src="{{ asset('assets/js/theme-loader.js') }}"></script>

<body>


  <!--start wrapper-->
  <div class="wrapper">
    <!--start top header-->
    @include('partials.header')
     <!--end top header-->

       <!--start sidebar -->
      @include('partials.sidebar')
       <!--end sidebar -->

       <!--start content-->
          @yield('content')
       <!--end page main-->


       <!--start overlay-->
        <div class="overlay nav-toggle-icon"></div>
       <!--end overlay-->

        <!--Start Back To Top Button-->
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
        <!--End Back To Top Button-->
        
        <!--start switcher-->
       <div class="switcher-body">
        <button class="btn btn-primary btn-switcher shadow-sm" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasScrolling" aria-controls="offcanvasScrolling"><i class="bi bi-paint-bucket me-0"></i></button>
        <div class="offcanvas offcanvas-end shadow border-start-0 p-2" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasScrolling">
          <div class="offcanvas-header border-bottom">
            <h5 class="offcanvas-title" id="offcanvasScrollingLabel">Theme Customizer</h5>
            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"></button>
          </div>
          <div class="offcanvas-body">
            <h6 class="mb-0">Theme Variation</h6>
            <hr>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="inlineRadioOptions" id="LightTheme" value="option1" checked>
              <label class="form-check-label" for="LightTheme">Light</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="inlineRadioOptions" id="DarkTheme" value="option2">
              <label class="form-check-label" for="DarkTheme">Dark</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="inlineRadioOptions" id="SemiDarkTheme" value="option3">
              <label class="form-check-label" for="SemiDarkTheme">Semi Dark</label>
            </div>
            <hr>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="inlineRadioOptions" id="MinimalTheme" value="option3">
              <label class="form-check-label" for="MinimalTheme">Minimal Theme</label>
            </div>
            <hr/>
            <h6 class="mb-0">Header Colors</h6>
            <hr/>
            <div class="header-colors-indigators">
              <div class="row row-cols-auto g-3">
                <div class="col">
                  <div class="indigator headercolor0" id="headercolor0"></div>
                </div>
                <div class="col">
                  <div class="indigator headercolor1" id="headercolor1"></div>
                </div>
                <div class="col">
                  <div class="indigator headercolor2" id="headercolor2"></div>
                </div>
                <div class="col">
                  <div class="indigator headercolor3" id="headercolor3"></div>
                </div>
                <div class="col">
                  <div class="indigator headercolor4" id="headercolor4"></div>
                </div>
                <div class="col">
                  <div class="indigator headercolor5" id="headercolor5"></div>
                </div>
                <div class="col">
                  <div class="indigator headercolor6" id="headercolor6"></div>
                </div>
                <div class="col">
                  <div class="indigator headercolor7" id="headercolor7"></div>
                </div>
                <div class="col">
                  <div class="indigator headercolor8" id="headercolor8"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
       </div>
       <!--end switcher-->

  </div>
  <!--end wrapper-->


  <!-- Bootstrap bundle JS -->
 @include('partials.scripts')
@stack('scripts')
  

</body>

</html>