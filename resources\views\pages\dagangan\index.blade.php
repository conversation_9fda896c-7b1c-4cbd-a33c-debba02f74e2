@extends('layouts.app')

@section('title', 'Dagangan')

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Data Master">
            <x-breadcrumb-item><PERSON>gangan</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal"><i
                    class="lni lni-plus"></i> Tambah</button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th><PERSON>a <PERSON></th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($dagangans as $dagangan)
                    <tr id="row_{{ $dagangan->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $dagangan->nama_dagangan }}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-warning edit-btn" 
                                data-id="{{ $dagangan->id }}">
                                <i class="lni lni-pencil"></i> Edit
                            </button>
                            <button type="button" class="btn btn-sm btn-danger deleteDagangan" 
                                data-id="{{ $dagangan->id }}">
                                <i class="lni lni-trash"></i> Hapus
                            </button>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Dagangan -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel"
        style="display: none;" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahModalLabel">Tambah Dagangan</h5>
                    <button type="button" class="btn-close addClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="namaDagangan" class="form-label">Nama Dagangan <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="namaDagangan" name="nama_dagangan"
                                placeholder="Nama Dagangan" >
                            <div class="invalid-feedback" id="errorNamaDagangan"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm addClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Dagangan -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel"
        style="display: none;" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Edit Dagangan</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editForm">
                    <input type="hidden" id="editDaganganId" name="id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editNamaDagangan" class="form-label">Nama Dagangan <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editNamaDagangan" name="nama_dagangan"
                                placeholder="Nama Dagangan" >
                            <div class="invalid-feedback" id="editErrorNamaDagangan"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // Handle form tambah dagangan
                $('#tambahForm').submit(function(e) {
                    e.preventDefault();

                    let namaDagangan = $('#namaDagangan').val();

                    // Reset validation
                    $('#namaDagangan').removeClass('is-invalid');
                    $('#errorNamaDagangan').text('');

                    $.ajax({
                        url: '{{ route('dagangan.store') }}',
                        method: 'POST',
                        data: {
                            nama_dagangan: namaDagangan
                        },
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahForm')[0].reset();
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;

                                if (errors.nama_dagangan) {
                                    $('#namaDagangan').addClass('is-invalid');
                                    $('#errorNamaDagangan').text(errors.nama_dagangan[0]);
                                }
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle edit button click
                $(document).on('click', '.edit-btn', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');

                    $.ajax({
                        url: '{{ route('dagangan.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editDaganganId').val(response.id);
                            $('#editNamaDagangan').val(response.nama_dagangan);
                            
                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data dagangan');
                        }
                    });
                });

                // Handle form edit dagangan
                $('#editForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editDaganganId').val();
                    let namaDagangan = $('#editNamaDagangan').val();

                    // Reset validation
                    $('#editNamaDagangan').removeClass('is-invalid');
                    $('#editErrorNamaDagangan').text('');

                    $.ajax({
                        url: '{{ route('dagangan.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: {
                            nama_dagangan: namaDagangan
                        },
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;

                                if (errors.nama_dagangan) {
                                    $('#editNamaDagangan').addClass('is-invalid');
                                    $('#editErrorNamaDagangan').text(errors.nama_dagangan[0]);
                                }
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle delete button click
                $(document).on('click', '.deleteDagangan', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus data dagangan ini?')) {
                        $.ajax({
                            url: '{{ route('dagangan.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                // Load table data function
                function loadTableData() {
                    $.ajax({
                        url: '{{ route('dagangan.index') }}',
                        method: 'GET',
                        success: function(response) {
                            let newTableBody = $(response).find('tbody').html();
                            $('tbody').html(newTableBody);
                        },
                        error: function() {
                            console.log('Error loading table data');
                        }
                    });
                }

                // Handle modal close events
                $('.addClose, .editClose').on('click', function() {
                    clearValidation('#tambahForm');
                    clearValidation('#editForm');
                });

                $('#tambahModal').on('hidden.bs.modal', function() {
                    clearValidation('#tambahForm');
                });

                $('#editModal').on('hidden.bs.modal', function() {
                    clearValidation('#editForm');
                });

                // Clear validation function
                function clearValidation(formId) {
                    $(formId).find('input, select').removeClass('is-invalid');
                    $(formId).find('.invalid-feedback').text('');
                    $(formId)[0].reset();
                }
            });
        </script>
    @endpush
@endsection
