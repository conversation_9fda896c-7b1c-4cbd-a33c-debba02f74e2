@extends('layouts.app')

@section('title', 'Kepal<PERSON> Dinas')

@section('content')
   <x-page-content>
        <x-page-breadcrumb title="Data Master">
            <x-breadcrumb-item><PERSON><PERSON><PERSON></x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal"><i
                    class="lni lni-plus"></i> Tambah</button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th>Nama</th>
                        <th>NIP</th>
                        <th>Golongan</th>
                        <th>Periode</th>
                        <th>Username</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($kepalaDinas as $item)
                    <tr id="row_{{ $item->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->nama }}</td>
                        <td>{{ $item->nip }}</td>
                        <td>{{ $item->golongan }}</td>
                        <td>{{ $item->periode }}</td>
                        <td>{{ $item->username }}</td>
                        <td>
                            @if ($item->status == 'aktif')
                                <span class="badge bg-success">Aktif</span>
                            @elseif($item->status == 'nonaktif')
                                <span class="badge bg-warning">Nonaktif</span>
                            @endif
                        </td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <i class="lni lni-more-alt"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item edit-btn" href="#" data-id="{{ $item->id }}">
                                            <i class="lni lni-pencil me-2"></i>Edit
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item text-danger deleteKepalaDinas" href="#"
                                            data-id="{{ $item->id }}">
                                            <i class="lni lni-trash me-2"></i>Hapus
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Kepala Dinas -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahModalLabel">Tambah Kepala Dinas</h5>
                    <button type="button" class="btn-close addClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="nama" class="form-label">Nama <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama" name="nama"
                                placeholder="Nama Kepala Dinas">
                            <div class="invalid-feedback" id="errorNama"></div>
                        </div>
                        <div class="mb-3">
                            <label for="nip" class="form-label">NIP <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nip" name="nip" placeholder="NIP">
                            <div class="invalid-feedback" id="errorNip"></div>
                        </div>
                        <div class="mb-3">
                            <label for="golongan" class="form-label">Golongan <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="golongan" name="golongan"
                                placeholder="Golongan">
                            <div class="invalid-feedback" id="errorGolongan"></div>
                        </div>
                        <div class="mb-3">
                            <label for="periode" class="form-label">Periode <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="periode" name="periode" placeholder="Periode">
                            <div class="invalid-feedback" id="errorPeriode"></div>
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label">Username <span class="text-danger">*</span"></label>
                            <input type="text" class="form-control" id="username" name="username"
                                placeholder="Username">
                            <div class="invalid-feedback" id="errorUsername"></div>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password"
                                placeholder="Password">
                            <div class="invalid-feedback" id="errorPassword"></div>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm addClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i> Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i> Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Kepala Dinas -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Kepala Dinas</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <form id="editForm">
                    <input type="hidden" id="editKepalaDinasId" name="id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editNama" class="form-label">Nama <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editNama" name="nama"
                                placeholder="Nama Kepala Dinas">
                            <div class="invalid-feedback" id="editErrorNama"></div>
                        </div>
                        <div class="mb-3">
                            <label for="editNip" class="form-label">NIP <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editNip" name="nip" placeholder="NIP">
                            <div class="invalid-feedback" id="editErrorNip"></div>
                        </div>
                        <div class="mb-3">
                            <label for="editGolongan" class="form-label">Golongan <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editGolongan" name="golongan"
                                placeholder="Golongan">
                            <div class="invalid-feedback" id="editErrorGolongan"></div>
                        </div>
                        <div class="mb-3">
                            <label for="editPeriode" class="form-label">Periode <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editPeriode" name="periode"
                                placeholder="Periode">
                            <div class="invalid-feedback" id="editErrorPeriode"></div>
                        </div>
                        <div class="mb-3">
                            <label for="editUsername" class="form-label">Username <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editUsername" name="username"
                                placeholder="Username">
                            <div class="invalid-feedback" id="editErrorUsername"></div>
                        </div>
                        <div class="mb-3">
                            <label for="editPassword" class="form-label">Password <span
                                    class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="editPassword" name="password"
                                placeholder="Password">
                            <div class="invalid-feedback" id="editErrorPassword"></div>
                        </div>
                        <div class="mb-3">
                            <label for="editStatus" class="form-label">Status <span class="text-danger">*</span></label>
                            <select class="form-select" id="editStatus" name="status">
                                <option value="">-- Pilih Status --</option>
                                <option value="aktif">Aktif</option>
                                <option value="nonaktif">Nonaktif</option>
                            </select>
                            <div class="invalid-feedback" id="editErrorStatus"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i> Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i> Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                $('#tambahForm').submit(function(e) {
                    e.preventDefault();

                    let formData = {
                        nama: $('#nama').val(),
                        nip: $('#nip').val(),
                        golongan: $('#golongan').val(),
                        periode: $('#periode').val(),
                        username: $('#username').val(),
                        password: $('#password').val(),
                    };

                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('kepala-dinas.store') }}',
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahForm')[0].reset();
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, '');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle edit button click
                $(document).on('click', '.edit-btn', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');
                    $.ajax({
                        url: '{{ route('kepala-dinas.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editKepalaDinasId').val(response.id);
                            $('#editNama').val(response.nama);
                            $('#editNip').val(response.nip);
                            $('#editGolongan').val(response.golongan);
                            $('#editPeriode').val(response.periode);
                            $('#editUsername').val(response.username);
                            $('#editStatus').val(response.status).trigger('change');
                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data Kepala Dinas');
                        }
                    });
                });

                // Handle form edit kepala dinas
                $('#editForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editKepalaDinasId').val();
                    let formData = {
                        nama: $('#editNama').val(),
                        nip: $('#editNip').val(),
                        golongan: $('#editGolongan').val(),
                        periode: $('#editPeriode').val(),
                        username: $('#editUsername').val(),
                        password: $('#editPassword').val(),
                        status: $('#editStatus').val(),
                    };

                    // Reset validation
                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('kepala-dinas.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: formData,
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'edit');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle delete kepala dinas
                $(document).on('click', '.deleteKepalaDinas', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus data kepala dinas ini?')) {
                        $.ajax({
                            url: '{{ route('kepala-dinas.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                function handleValidationErrors(errors, prefix) {
                    const fieldMap = {
                        'nama': prefix + 'Nama',
                        'nip': prefix + 'Nip',
                        'golongan': prefix + 'Golongan',
                        'periode': prefix + 'Periode',
                        'username': prefix + 'Username',
                        'password': prefix + 'Password',
                        'status': prefix + 'Status',
                    };

                    Object.keys(errors).forEach(function(key) {
                        if (fieldMap[key]) {
                            let inputId = fieldMap[key].charAt(0).toLowerCase() + fieldMap[key].slice(1);
                            $('#' + inputId).addClass('is-invalid');

                            let errorId = prefix ? 'editError' + fieldMap[key].replace(prefix, '') : 'error' +
                                fieldMap[key];
                            $('#' + errorId).text(errors[key][0]);
                        }
                    });
                }

                function clearValidation(formId) {
                    $(formId).find('input, select').removeClass('is-invalid');
                    $(formId).find('.invalid-feedback').text('');
                    $(formId)[0].reset();
                }

                function loadTableData() {
                    $.ajax({
                        url: '{{ route('kepala-dinas.index') }}',
                        method: 'GET',
                        success: function(response) {
                            let newTableBody = $(response).find('tbody').html();
                            $('tbody').html(newTableBody);
                        },
                        error: function() {
                            console.log('Error loading table data');
                        }
                    });
                }
            });
        </script>
    @endpush
@endsection
