@extends('layouts.app')

@section('title', 'Kepala Pasar')

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Data Master">
            <x-breadcrumb-item>Kepala Pasar</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal"><i
                    class="lni lni-plus"></i> Tambah</button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th>Nama <PERSON>sar</th>
                        <th>Nama</th>
                        <th>Username</th>
                        <th>NIP</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($kepalaPasar as $item)
                    <tr id="row_{{ $item->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->pasar->nama_pasar }}</td>
                        <td>{{ $item->nama }}</td>
                        <td>{{ $item->username }}</td>
                        <td>{{ $item->nip }}</td>
                        <td>
                            @if ($item->status == 'aktif')
                                <span class="badge bg-success">Aktif</span>
                            @elseif($item->status == 'nonaktif')
                                <span class="badge bg-warning">Nonaktif</span>
                            @endif
                        </td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <i class="lni lni-more-alt"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item edit-btn" href="#" data-id="{{ $item->id }}">
                                            <i class="lni lni-pencil me-2"></i>Edit
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item text-danger deleteKepalaPasar" href="#"
                                            data-id="{{ $item->id }}">
                                            <i class="lni lni-trash me-2"></i>Hapus
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Kepala Pasar -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Tambah Kepala Pasar</h5>
                    <button type="button" class="btn-close addClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahForm">
                    <div class="modal-body">

                        <div class="mb-3">
                            <label for="pasar" class="form-label">Pilih Pasar <span class="text-danger">*</span></label>
                            <select class="form-select" id="pasar" name="pasar_id">
                                <option value="">-- Pilih Pasar --</option>
                                @foreach ($pasars as $pasar)
                                    <option value="{{ $pasar->id }}">{{ $pasar->nama_pasar }}</option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback" id="errorPasar"></div>
                        </div>


                        <div class="mb-3">
                            <label for="nama" class="form-label">Nama <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama" name="nama"
                                placeholder="Nama Kepala Pasar">
                            <div class="invalid-feedback" id="errorNama"></div>
                        </div>

                        <div class="mb-3">
                            <label for="nip" class="form-label">NIP <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nip" name="nip" placeholder="NIP">
                            <div class="invalid-feedback" id="errorNip"></div>
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username"
                                placeholder="Username">
                            <div class="invalid-feedback" id="errorUsername"></div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password"
                                placeholder="Password">
                            <div class="invalid-feedback" id="errorPassword"></div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm addClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i> Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i> Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <!-- Modal Edit Kios -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Kepala Pasar</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <form id="editForm">
                    <input type="hidden" id="editKepalaPasarId" name="id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editPasar" class="form-label">Pilih Pasar <span
                                    class="text-danger">*</span></label>
                            <select class="form-select" id="editPasar" name="pasar_id">
                                <option value="">-- Pilih Pasar --</option>
                                @foreach ($pasars as $pasar)
                                    <option value="{{ $pasar->id }}">{{ $pasar->nama_pasar }}</option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback" id="editErrorPasar"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editNama" class="form-label">Nama <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editNama" name="nama"
                                placeholder="Nama Kepala Pasar">
                            <div class="invalid-feedback" id="editErrorNama"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editNip" class="form-label">NIP <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editNip" name="nip" placeholder="NIP">
                            <div class="invalid-feedback" id="editErrorNip"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editUsername" class="form-label">Username <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editUsername" name="username"
                                placeholder="Username">
                            <div class="invalid-feedback" id="editErrorUsername"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editStatus" class="form-label">Status <span class="text-danger">*</span"></label>
                            <select class="form-select" id="editStatus" name="status">
                                <option value="">-- Pilih Status --</option>
                                <option value="aktif">Aktif</option>
                                <option value="nonaktif">Nonaktif</option>
                            </select>
                            <div class="invalid-feedback" id="editErrorStatus"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editPassword" class="form-label">Password (Opsional)</label>
                            <input type="password" class="form-control" id="editPassword" name="password"
                                placeholder="Kosongkan jika tidak diubah">
                            <div class="invalid-feedback" id="editErrorPassword"></div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i> Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i> Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // Handle form tambah kios
                $('#tambahForm').submit(function(e) {
                    e.preventDefault();

                    let formData = {
                        pasar_id: $('#pasar').val(),
                        nama: $('#nama').val(),
                        nip: $('#nip').val(),
                        username: $('#username').val(),
                        password: $('#password').val()
                    };

                    // Reset feedback
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('kepala-pasar.store') }}',
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahForm')[0].reset();
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, '');
                            } else {
                                alert('Terjadi kesalahan saat menyimpan data.');
                            }
                        }
                    });
                });


                // Handle edit button click
                $(document).on('click', '.edit-btn', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');
                    $.ajax({
                        url: '{{ route('kepala-pasar.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editKepalaPasarId').val(response.id);
                            $('#editPasar').val(response.pasar_id).trigger('change');
                            $('#editNama').val(response.nama);
                            $('#editNip').val(response.nip);
                            $('#editUsername').val(response.username);
                            $('#editStatus').val(response.status).trigger('change');
                            $('#editPassword').val(
                                '');

                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data Kepala Pasar');
                        }
                    });
                });


                // Handle form edit kios
                $('#editForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editKepalaPasarId').val();
                    let formData = {
                        pasar_id: $('#editPasar').val(),
                        nama: $('#editNama').val(),
                        nip: $('#editNip').val(),
                        username: $('#editUsername').val(),
                        password: $('#editPassword').val(),
                        status: $('#editStatus').val()
                    };

                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('kepala-pasar.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: formData,
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'edit');
                            } else {
                                alert('Terjadi kesalahan saat mengupdate data.');
                            }
                        }
                    });
                });


                // Handle delete button click
                $(document).on('click', '.deleteKepalaPasar', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus data kepala pasar ini?')) {
                        $.ajax({
                            url: '{{ route('kepala-pasar.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                // Handle validation errors
                function handleValidationErrors(errors, prefix) {
                    const fieldMap = {
                        'pasar_id': prefix + 'Pasar',
                        'nama': prefix + 'Nama',
                        'nip': prefix + 'Nip',
                        'username': prefix + 'Username',
                        'password': prefix + 'Password',
                        'status': prefix + 'Status'
                    };

                    Object.keys(errors).forEach(function(key) {
                        if (fieldMap[key]) {
                            let inputId = fieldMap[key].charAt(0).toLowerCase() + fieldMap[key].slice(1);
                            $('#' + inputId).addClass('is-invalid');

                            let errorId = prefix ? 'editError' + fieldMap[key].replace(prefix, '') : 'error' +
                                fieldMap[key];
                            $('#' + errorId).text(errors[key][0]);
                        }
                    });
                }


                // Handle modal close events
                $('.addClose, .editClose').on('click', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                $('#tambahModal, #editModal').on('hidden.bs.modal', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                function loadTableData() {
                    $.ajax({
                        url: '{{ route('kepala-pasar.index') }}',
                        method: 'GET',
                        success: function(response) {
                            let newTableBody = $(response).find('tbody').html();
                            $('tbody').html(newTableBody);
                        },
                        error: function() {
                            console.log('Error loading table data');
                        }
                    });
                }

                // Initialize Select2 for modals
                $('#pasar').select2({
                    dropdownParent: $('#tambahModal'),
                    theme: 'bootstrap4'
                });

                $('#editPasar').select2({
                    dropdownParent: $('#editModal'),
                    theme: 'bootstrap4'
                });

            });
        </script>
    @endpush
@endsection
