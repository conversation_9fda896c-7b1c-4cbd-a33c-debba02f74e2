@extends('layouts.app')

@section('title', 'Data Kios')

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Data Master">
            <x-breadcrumb-item>Kios</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal"><i
                    class="lni lni-plus"></i> Tambah</button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th>Nomor <PERSON></th>
                        <th>Nama Blok</th>
                        <th>Lokasi</th>
                        <th>Pasar</th>
                        <th>Tarif</th>
                        <th>Ukuran (m)</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($kios as $item)
                    <tr id="row_{{ $item->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->nomor }}</td>
                        <td>{{ $item->nama_blok }}</td>
                        <td>{{ $item->lokasi ?? '-' }}</td>
                        <td>{{ $item->pasar->nama_pasar ?? '-' }}</td>
                        <td>Rp {{ number_format($item->tarif->tarif ?? 0, 0, ',', '.') }}</td>
                        <td>{{ $item->lebar }} x {{ $item->panjang }}</td>
                        <td>
                            @if ($item->status == 'tersedia')
                                <span class="badge bg-success">Tersedia</span>
                            @elseif($item->status == 'terisi')
                                <span class="badge bg-warning">Terisi</span>
                            @else
                                <span class="badge bg-danger">Diblokir</span>
                            @endif
                        </td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <i class="lni lni-more-alt"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item edit-btn" href="#" data-id="{{ $item->id }}">
                                            <i class="lni lni-pencil me-2"></i>Edit
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item text-danger deleteKios" href="#"
                                            data-id="{{ $item->id }}">
                                            <i class="lni lni-trash me-2"></i>Hapus
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Kios -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahModalLabel">Tambah Kios</h5>
                    <button type="button" class="btn-close addClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="pasarKios" class="form-label">Pilih Pasar <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="pasarKios" name="pasar_id" >
                                        <option value="">-- Pilih Pasar --</option>
                                        @foreach ($pasars as $pasar)
                                            <option value="{{ $pasar->id }}">{{ $pasar->nama_pasar }}</option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="errorPasarKios"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tarifKios" class="form-label">Pilih Tarif <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="tarifKios" name="tarif_id" >
                                        <option value="">-- Pilih Tarif --</option>
                                        @foreach ($tarifs as $tarif)
                                            <option value="{{ $tarif->id }}">{{ $tarif->jenis }} Tipe
                                                {{ $tarif->tipe }} - Rp {{ number_format($tarif->tarif, 0, ',', '.') }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="errorTarifKios"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nomorKios" class="form-label">Nomor Kios <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nomorKios" name="nomor"
                                        placeholder="Nomor Kios" >
                                    <div class="invalid-feedback" id="errorNomorKios"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="namaBlokKios" class="form-label">Nama Blok <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="namaBlokKios" name="nama_blok"
                                        placeholder="Nama Blok" >
                                    <div class="invalid-feedback" id="errorNamaBlokKios"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="lokasiKios" class="form-label">Lokasi <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="lokasiKios" name="lokasi"
                                        placeholder="Lokasi">
                                    <div class="invalid-feedback" id="errorLokasiKios"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="lebarKios" class="form-label">Lebar (m) <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="lebarKios" name="lebar"
                                        placeholder="Lebar" step="0.01" min="0" >
                                    <div class="invalid-feedback" id="errorLebarKios"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="panjangKios" class="form-label">Panjang (m) <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="panjangKios" name="panjang"
                                        placeholder="Panjang" step="0.01" min="0" >
                                    <div class="invalid-feedback" id="errorPanjangKios"></div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm addClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Kios -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Edit Kios</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <form id="editForm">
                    <input type="hidden" id="editKiosId" name="id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editPasarKios" class="form-label">Pilih Pasar <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editPasarKios" name="pasar_id" >
                                        <option value="">-- Pilih Pasar --</option>
                                        @foreach ($pasars as $pasar)
                                            <option value="{{ $pasar->id }}">{{ $pasar->nama_pasar }}</option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="editErrorPasarKios"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editTarifKios" class="form-label">Pilih Tarif <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editTarifKios" name="tarif_id" >
                                        <option value="">-- Pilih Tarif --</option>
                                        @foreach ($tarifs as $tarif)
                                            <option value="{{ $tarif->id }}">{{ $tarif->jenis }} Tipe
                                                {{ $tarif->tipe }} - Rp {{ number_format($tarif->tarif, 0, ',', '.') }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="editErrorTarifKios"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNomorKios" class="form-label">Nomor Kios <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editNomorKios" name="nomor"
                                        placeholder="Nomor Kios" >
                                    <div class="invalid-feedback" id="editErrorNomorKios"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNamaBlokKios" class="form-label">Nama Blok <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editNamaBlokKios" name="nama_blok"
                                        placeholder="Nama Blok" >
                                    <div class="invalid-feedback" id="editErrorNamaBlokKios"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="editLokasiKios" class="form-label">Lokasi <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editLokasiKios" name="lokasi"
                                        placeholder="Lokasi">
                                    <div class="invalid-feedback" id="editErrorLokasiKios"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editLebarKios" class="form-label">Lebar (m) <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="editLebarKios" name="lebar"
                                        placeholder="Lebar" step="0.01" min="0" >
                                    <div class="invalid-feedback" id="editErrorLebarKios"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editPanjangKios" class="form-label">Panjang (m) <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="editPanjangKios" name="panjang"
                                        placeholder="Panjang" step="0.01" min="0" >
                                    <div class="invalid-feedback" id="editErrorPanjangKios"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editStatusKios" class="form-label">Status <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editStatusKios" name="status" >
                                        <option value="">-- Pilih Status --</option>
                                        <option value="tersedia">Tersedia</option>
                                        <option value="terisi">Terisi</option>
                                        <option value="diblokir">Diblokir</option>
                                    </select>
                                    <div class="invalid-feedback" id="editErrorStatusKios"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // Handle form tambah kios
                $('#tambahForm').submit(function(e) {
                    e.preventDefault();

                    let formData = {
                        pasar_id: $('#pasarKios').val(),
                        tarif_id: $('#tarifKios').val(),
                        nomor: $('#nomorKios').val(),
                        nama_blok: $('#namaBlokKios').val(),
                        lokasi: $('#lokasiKios').val(),
                        lebar: $('#lebarKios').val(),
                        panjang: $('#panjangKios').val(),
                        status: $('#statusKios').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('kios.store') }}',
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahForm')[0].reset();
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, '');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle edit button click
                $(document).on('click', '.edit-btn', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');
                    $.ajax({
                        url: '{{ route('kios.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editKiosId').val(response.id);
                            $('#editPasarKios').val(response.pasar_id).trigger('change');
                            $('#editTarifKios').val(response.tarif_id).trigger('change');
                            $('#editNomorKios').val(response.nomor);
                            $('#editNamaBlokKios').val(response.nama_blok);
                            $('#editLokasiKios').val(response.lokasi);
                            $('#editLebarKios').val(response.lebar);
                            $('#editPanjangKios').val(response.panjang);
                            $('#editStatusKios').val(response.status);

                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data kios');
                        }
                    });
                });

                // Handle form edit kios
                $('#editForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editKiosId').val();
                    let formData = {
                        pasar_id: $('#editPasarKios').val(),
                        tarif_id: $('#editTarifKios').val(),
                        nomor: $('#editNomorKios').val(),
                        nama_blok: $('#editNamaBlokKios').val(),
                        lokasi: $('#editLokasiKios').val(),
                        lebar: $('#editLebarKios').val(),
                        panjang: $('#editPanjangKios').val(),
                        status: $('#editStatusKios').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('kios.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: formData,
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'edit');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle delete button click
                $(document).on('click', '.deleteKios', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus data kios ini?')) {
                        $.ajax({
                            url: '{{ route('kios.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                // Handle validation errors
                function handleValidationErrors(errors, prefix) {
                    const fieldMap = {
                        'pasar_id': prefix + 'PasarKios',
                        'tarif_id': prefix + 'TarifKios',
                        'nomor': prefix + 'NomorKios',
                        'nama_blok': prefix + 'NamaBlokKios',
                        'lokasi': prefix + 'LokasiKios',
                        'lebar': prefix + 'LebarKios',
                        'panjang': prefix + 'PanjangKios',
                        'status': prefix + 'StatusKios'
                    };

                    Object.keys(errors).forEach(function(key) {
                        if (fieldMap[key]) {
                            // Input field pakai huruf kecil
                            let inputId = fieldMap[key].charAt(0).toLowerCase() + fieldMap[key].slice(1);
                            $('#' + inputId).addClass('is-invalid');

                            // Error ID tetap pakai format original
                            let errorId = prefix ? 'editError' + fieldMap[key].replace(prefix, '') : 'error' +
                                fieldMap[key];
                            $('#' + errorId).text(errors[key][0]);
                        }
                    });
                }

                // Handle modal close events
                $('.addClose, .editClose').on('click', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                $('#tambahModal, #editModal').on('hidden.bs.modal', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                function loadTableData() {
                    $.ajax({
                        url: '{{ route('kios.index') }}',
                        method: 'GET',
                        success: function(response) {
                            let newTableBody = $(response).find('tbody').html();
                            $('tbody').html(newTableBody);
                        },
                        error: function() {
                            console.log('Error loading table data');
                        }
                    });
                }

                // Initialize Select2 for modals
                $('#pasarKios, #tarifKios').select2({
                    dropdownParent: $('#tambahModal'),
                    theme: 'bootstrap4'
                });

                $('#editPasarKios, #editTarifKios').select2({
                    dropdownParent: $('#editModal'),
                    theme: 'bootstrap4'
                });

                // Filter tarif berdasarkan pasar yang dipilih
                $('#pasarKios').on('change', function(e) {
                    e.preventDefault();
                    let pasar_id = $(this).val();
                    $('#tarifKios').empty().append('<option value="">-- Pilih Tarif --</option>');

                    if (pasar_id) {
                        $.ajax({
                            url: '{{ route('kios.getTarifByPasar') }}',
                            method: 'POST',
                            data: {
                                pasar_id: pasar_id,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                $.each(response.tarifs, function(index, tarif) {
                                    $('#tarifKios').append('<option value="' + tarif.id +
                                        '">' +
                                        tarif.jenis + ' Tipe ' + tarif.tipe + ' - Rp ' +
                                        new Intl.NumberFormat('id-ID').format(tarif
                                            .tarif) + '</option>');
                                });
                            }
                        });
                    }
                });

                $('#editPasarKios').on('change', function(e) {
                    e.preventDefault();
                    let pasar_id = $(this).val();
                    $('#editTarifKios').empty().append('<option value="">-- Pilih Tarif --</option>');

                    if (pasar_id) {
                        $.ajax({
                            url: '{{ route('kios.getTarifByPasar') }}',
                            method: 'POST',
                            data: {
                                pasar_id: pasar_id,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                $.each(response.tarifs, function(index, tarif) {
                                    $('#editTarifKios').append('<option value="' + tarif
                                        .id + '">' +
                                        tarif.jenis + ' Tipe ' + tarif.tipe + ' - Rp ' +
                                        new Intl.NumberFormat('id-ID').format(tarif
                                            .tarif) + '</option>');
                                });
                            }
                        });
                    }
                });
            });
        </script>
    @endpush
