@extends('layouts.app')

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Data Master">
            <x-breadcrumb-item>Los</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal"><i
                    class="lni lni-plus"></i> Tambah</button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th>Nomor Los</th>
                        <th>Nama <PERSON>lok</th>
                        <th>Lokasi</th>
                        <th>Pasar</th>
                        <th>Tarif</th>
                        <th>Ukuran (m)</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($los as $item)
                    <tr id="row_{{ $item->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->nomor }}</td>
                        <td>{{ $item->nama_blok }}</td>
                        <td>{{ $item->lokasi ?? '-' }}</td>
                        <td>{{ $item->pasar->nama_pasar ?? '-' }}</td>
                        <td>Rp {{ number_format($item->tarif->tarif ?? 0, 0, ',', '.') }}</td>
                        <td>{{ $item->lebar }} x {{ $item->panjang }}</td>
                        <td>
                            @if ($item->status == 'tersedia')
                                <span class="badge bg-success">Tersedia</span>
                            @elseif($item->status == 'terisi')
                                <span class="badge bg-warning">Terisi</span>
                            @else
                                <span class="badge bg-danger">Diblokir</span>
                            @endif
                        </td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <i class="lni lni-more-alt"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item edit-btn" href="#" data-id="{{ $item->id }}">
                                            <i class="lni lni-pencil me-2"></i>Edit
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item text-danger deleteLos" href="#"
                                            data-id="{{ $item->id }}">
                                            <i class="lni lni-trash me-2"></i>Hapus
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Los -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahModalLabel">Tambah Los</h5>
                    <button type="button" class="btn-close addClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="pasarLos" class="form-label">Pilih Pasar <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select single-select" id="pasarLos" name="pasar_id" required>
                                        <option value="">-- Pilih Pasar --</option>
                                        @foreach ($pasars as $pasar)
                                            <option value="{{ $pasar->id }}">{{ $pasar->nama_pasar }}</option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="errorPasarLos"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tarifLos" class="form-label">Pilih Tarif <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select single-select" id="tarifLos" name="tarif_id" required>
                                        <option value="">-- Pilih Tarif --</option>
                                        @foreach ($tarifs as $tarif)
                                            <option value="{{ $tarif->id }}">{{ $tarif->jenis }} Tipe
                                                {{ $tarif->tipe }} - Rp {{ number_format($tarif->tarif, 0, ',', '.') }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="errorTarifLos"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nomorLos" class="form-label">Nomor Los <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nomorLos" name="nomor"
                                        placeholder="Nomor Los" required>
                                    <div class="invalid-feedback" id="errorNomorLos"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="namaBlokLos" class="form-label">Nama Blok <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="namaBlokLos" name="nama_blok"
                                        placeholder="Nama Blok" required>
                                    <div class="invalid-feedback" id="errorNamaBlokLos"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="lokasiLos" class="form-label">Lokasi</label>
                                    <input type="text" class="form-control" id="lokasiLos" name="lokasi"
                                        placeholder="Lokasi (opsional)">
                                    <div class="invalid-feedback" id="errorLokasiLos"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="lebarLos" class="form-label">Lebar (m) <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="lebarLos" name="lebar"
                                        placeholder="Lebar" step="0.01" min="0" required>
                                    <div class="invalid-feedback" id="errorLebarLos"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="panjangLos" class="form-label">Panjang (m) <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="panjangLos" name="panjang"
                                        placeholder="Panjang" step="0.01" min="0" required>
                                    <div class="invalid-feedback" id="errorPanjangLos"></div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm addClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Los -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Edit Los</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <form id="editForm">
                    <input type="hidden" id="editLosId" name="id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editPasarLos" class="form-label">Pilih Pasar <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editPasarLos" name="pasar_id" required>
                                        <option value="">-- Pilih Pasar --</option>
                                        @foreach ($pasars as $pasar)
                                            <option value="{{ $pasar->id }}">{{ $pasar->nama_pasar }}</option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="editErrorPasarLos"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editTarifLos" class="form-label">Pilih Tarif <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editTarifLos" name="tarif_id" required>
                                        <option value="">-- Pilih Tarif --</option>
                                        @foreach ($tarifs as $tarif)
                                            <option value="{{ $tarif->id }}">{{ $tarif->jenis }} Tipe
                                                {{ $tarif->tipe }} - Rp {{ number_format($tarif->tarif, 0, ',', '.') }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="editErrorTarifLos"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNomorLos" class="form-label">Nomor Los <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editNomorLos" name="nomor"
                                        placeholder="Nomor Los" required>
                                    <div class="invalid-feedback" id="editErrorNomorLos"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNamaBlokLos" class="form-label">Nama Blok <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editNamaBlokLos" name="nama_blok"
                                        placeholder="Nama Blok" required>
                                    <div class="invalid-feedback" id="editErrorNamaBlokLos"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="editLokasiLos" class="form-label">Lokasi</label>
                                    <input type="text" class="form-control" id="editLokasiLos" name="lokasi"
                                        placeholder="Lokasi (opsional)">
                                    <div class="invalid-feedback" id="editErrorLokasiLos"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editLebarLos" class="form-label">Lebar (m) <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="editLebarLos" name="lebar"
                                        placeholder="Lebar" step="0.01" min="0" required>
                                    <div class="invalid-feedback" id="editErrorLebarLos"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editPanjangLos" class="form-label">Panjang (m) <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="editPanjangLos" name="panjang"
                                        placeholder="Panjang" step="0.01" min="0" required>
                                    <div class="invalid-feedback" id="editErrorPanjangLos"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editStatusLos" class="form-label">Status <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editStatusLos" name="status" required>
                                        <option value="">-- Pilih Status --</option>
                                        <option value="tersedia">Tersedia</option>
                                        <option value="terisi">Terisi</option>
                                        <option value="diblokir">Diblokir</option>
                                    </select>
                                    <div class="invalid-feedback" id="editErrorStatusLos"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // Handle form tambah los
                $('#tambahForm').submit(function(e) {
                    e.preventDefault();

                    let formData = {
                        pasar_id: $('#pasarLos').val(),
                        tarif_id: $('#tarifLos').val(),
                        nomor: $('#nomorLos').val(),
                        nama_blok: $('#namaBlokLos').val(),
                        lokasi: $('#lokasiLos').val(),
                        lebar: $('#lebarLos').val(),
                        panjang: $('#panjangLos').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('los.store') }}',
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahForm')[0].reset();
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, '');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle edit button click
                $(document).on('click', '.edit-btn', function() {
                    let id = $(this).data('id');
                    $.ajax({
                        url: '{{ route('los.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editLosId').val(response.id);
                            $('#editPasarLos').val(response.pasar_id);
                            $('#editTarifLos').val(response.tarif_id);
                            $('#editNomorLos').val(response.nomor);
                            $('#editNamaBlokLos').val(response.nama_blok);
                            $('#editLokasiLos').val(response.lokasi);
                            $('#editLebarLos').val(response.lebar);
                            $('#editPanjangLos').val(response.panjang);
                            $('#editStatusLos').val(response.status);

                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data los');
                        }
                    });
                });

                // Handle form edit los
                $('#editForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editLosId').val();
                    let formData = {
                        pasar_id: $('#editPasarLos').val(),
                        tarif_id: $('#editTarifLos').val(),
                        nomor: $('#editNomorLos').val(),
                        nama_blok: $('#editNamaBlokLos').val(),
                        lokasi: $('#editLokasiLos').val(),
                        lebar: $('#editLebarLos').val(),
                        panjang: $('#editPanjangLos').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('los.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: formData,
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'edit');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle delete button click
                $(document).on('click', '.deleteLos', function() {
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus data los ini?')) {
                        $.ajax({
                            url: '{{ route('los.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                // Handle validation errors
                function handleValidationErrors(errors, prefix) {
                    const fieldMap = {
                        'pasar_id': prefix + 'PasarLos',
                        'tarif_id': prefix + 'TarifLos',
                        'nomor': prefix + 'NomorLos',
                        'nama_blok': prefix + 'NamaBlokLos',
                        'lokasi': prefix + 'LokasiLos',
                        'lebar': prefix + 'LebarLos',
                        'panjang': prefix + 'PanjangLos'
                    };

                    Object.keys(errors).forEach(function(key) {
                        if (fieldMap[key]) {
                            $('#' + fieldMap[key]).addClass('is-invalid');
                            let errorId = prefix ? 'editError' + fieldMap[key].replace(prefix, '') : 'error' +
                                fieldMap[key];
                            $('#' + errorId).text(errors[key][0]);
                        }
                    });
                }

                // Handle modal close events
                $('.addClose, .editClose').on('click', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                $('#tambahModal, #editModal').on('hidden.bs.modal', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                function loadTableData() {
                    location.reload();
                }

                // Initialize Select2 for modals
                $('#pasarLos, #tarifLos').select2({
                    dropdownParent: $('#tambahModal'),
                    theme: 'bootstrap4'
                });

                $('#editPasarLos, #editTarifLos').select2({
                    dropdownParent: $('#editModal'),
                    theme: 'bootstrap4'
                });

                // Filter tarif berdasarkan pasar yang dipilih
                $('#pasarLos').on('change', function() {
                    let pasar_id = $(this).val();
                    $('#tarifLos').empty().append('<option value="">-- Pilih Tarif --</option>');

                    if (pasar_id) {
                        $.ajax({
                            url: '{{ route('los.getTarifByPasar') }}',
                            method: 'POST',
                            data: {
                                pasar_id: pasar_id,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                $.each(response.tarifs, function(index, tarif) {
                                    $('#tarifLos').append('<option value="' + tarif.id + '">' +
                                        tarif.jenis + ' Tipe ' + tarif.tipe + ' - Rp ' +
                                        new Intl.NumberFormat('id-ID').format(tarif.tarif) + '</option>');
                                });
                            }
                        });
                    }
                });

                $('#editPasarLos').on('change', function() {
                    let pasar_id = $(this).val();
                    $('#editTarifLos').empty().append('<option value="">-- Pilih Tarif --</option>');

                    if (pasar_id) {
                        $.ajax({
                            url: '{{ route('los.getTarifByPasar') }}',
                            method: 'POST',
                            data: {
                                pasar_id: pasar_id,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                $.each(response.tarifs, function(index, tarif) {
                                    $('#editTarifLos').append('<option value="' + tarif.id + '">' +
                                        tarif.jenis + ' Tipe ' + tarif.tipe + ' - Rp ' +
                                        new Intl.NumberFormat('id-ID').format(tarif.tarif) + '</option>');
                                });
                            }
                        });
                    }
                });
            });
        </script>
    @endpush
