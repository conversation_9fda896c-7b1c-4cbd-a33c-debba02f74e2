@extends('layouts.app')

@section('title', 'Data Pasar')

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Data Master">
            <x-breadcrumb-item>Pasar</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal"><i
                    class="lni lni-plus"></i> Tambah</button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th><PERSON>a <PERSON></th>
                        <th>Tipe Pasar</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($pasars as $pasar)
                    <tr id="row_{{ $pasar->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $pasar->nama_pasar }}</td>
                        <td>Tipe {{ $pasar->tipe_pasar }}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-warning edit-btn"
                                data-id="{{ $pasar->id }}">
                                <i class="lni lni-pencil"></i> Edit
                            </button>
                            <button type="button" class="btn btn-sm btn-danger deletePasar"
                                data-id="{{ $pasar->id }}">
                                <i class="lni lni-trash"></i> Hapus
                            </button>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Pasar -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel"
        style="display: none;" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahModalLabel">Tambah Pasar</h5>
                    <button type="button" class="btn-close addClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="namaPasar" class="form-label">Nama Pasar <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="namaPasar" name="nama_pasar"
                                placeholder="Nama Pasar" >
                            <div class="invalid-feedback" id="errorNamaPasar"></div>
                        </div>
                        <div class="mb-3">
                            <label for="tipePasar" class="form-label">Pilih Tipe <span
                                    class="text-danger">*</span></label>
                            <select class="form-select" id="tipePasar" name="tipe_pasar" >
                                <option value="">-- Pilih Tipe --</option>
                                <option value="A">Tipe A</option>
                                <option value="B">Tipe B</option>
                                <option value="C">Tipe C</option>
                                <option value="D">Tipe D</option>
                            </select>
                            <div class="invalid-feedback" id="errorTipePasar"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm addClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Pasar -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel"
        style="display: none;" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Edit Pasar</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editForm">
                    <input type="hidden" id="editPasarId" name="id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editNamaPasar" class="form-label">Nama Pasar <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editNamaPasar" name="nama_pasar"
                                placeholder="Nama Pasar" >
                            <div class="invalid-feedback" id="editErrorNamaPasar"></div>
                        </div>
                        <div class="mb-3">
                            <label for="editTipePasar" class="form-label">Pilih Tipe <span
                                    class="text-danger">*</span></label>
                            <select class="form-select" id="editTipePasar" name="tipe_pasar" >
                                <option value="">-- Pilih Tipe --</option>
                                <option value="A">Tipe A</option>
                                <option value="B">Tipe B</option>
                                <option value="C">Tipe C</option>
                                <option value="D">Tipe D</option>
                            </select>
                            <div class="invalid-feedback" id="editErrorTipePasar"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // Handle form tambah pasar
                $('#tambahForm').submit(function(e) {
                    e.preventDefault();

                    let namaPasar = $('#namaPasar').val();
                    let tipePasar = $('#tipePasar').val();

                    // Reset validation
                    $('#namaPasar').removeClass('is-invalid');
                    $('#tipePasar').removeClass('is-invalid');
                    $('#errorNamaPasar').text('');
                    $('#errorTipePasar').text('');

                    $.ajax({
                        url: '{{ route('pasar.store') }}',
                        method: 'POST',
                        data: {
                            nama_pasar: namaPasar,
                            tipe_pasar: tipePasar
                        },
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahForm')[0].reset();
                            round_success_noti(response.success);

                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;

                                if (errors.nama_pasar) {
                                    $('#namaPasar').addClass('is-invalid');
                                    $('#errorNamaPasar').text(errors.nama_pasar[0]);
                                }
                                if (errors.tipe_pasar) {
                                    $('#tipePasar').addClass('is-invalid');
                                    $('#errorTipePasar').text(errors.tipe_pasar[0]);
                                }
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                $(document).on('click', '.edit-btn', function() {
                    let id = $(this).data('id');
                    $.ajax({
                        url: '{{ route('pasar.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editPasarId').val(response.id);
                            $('#editNamaPasar').val(response.nama_pasar);
                            $('#editTipePasar').val(response.tipe_pasar);

                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data pasar');
                        }
                    });
                });


                $('#editForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editPasarId').val();
                    let namaPasar = $('#editNamaPasar').val();
                    let tipePasar = $('#editTipePasar').val();

                    // Reset validation
                    $('#editNamaPasar').removeClass('is-invalid');
                    $('#editTipePasar').removeClass('is-invalid');
                    $('#editErrorNamaPasar').text('');
                    $('#editErrorTipePasar').text('');

                    $.ajax({
                        url: '{{ route('pasar.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: {
                            nama_pasar: namaPasar,
                            tipe_pasar: tipePasar
                        },
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;

                                if (errors.nama_pasar) {
                                    $('#editNamaPasar').addClass('is-invalid');
                                    $('#editErrorNamaPasar').text(errors.nama_pasar[0]);
                                }
                                if (errors.tipe_pasar) {
                                    $('#editTipePasar').addClass('is-invalid');
                                    $('#editErrorTipePasar').text(errors.tipe_pasar[0]);
                                }
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle delete button click
                $(document).on('click', '.deletePasar', function() {
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus data pasar ini?')) {
                        $.ajax({
                            url: '{{ route('pasar.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                // Handle modal close events
                $('.addClose, .editClose').on('click', function() {
                    clearValidation('#tambahForm');
                    clearValidation('#editForm');
                });

                $('#tambahModal').on('hidden.bs.modal', function() {
                    clearValidation('#tambahForm');
                });

                $('#editModal').on('hidden.bs.modal', function() {
                    clearValidation('#editForm');
                });

                // Load table data function
                function loadTableData() {
                    $.ajax({
                        url: '{{ route('pasar.index') }}',
                        method: 'GET',
                        success: function(response) {
                            let newTableBody = $(response).find('tbody').html();
                            $('tbody').html(newTableBody);
                        },
                        error: function() {
                            console.log('Error loading table data');
                        }
                    });
                }

                
                function clearValidation(formId) {
                    $(formId).find('input, select').removeClass('is-invalid');
                    $(formId).find('.invalid-feedback').text('');
                    $(formId)[0].reset();
                }
            });
        </script>
    @endpush
@endsection
