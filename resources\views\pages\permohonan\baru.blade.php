@extends('layouts.app')

@section('title', 'Permohonan Baru')

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Permohonan">
            <x-breadcrumb-item>Permohonan Baru</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal">
                <i class="lni lni-plus"></i> Tambah Permohonan
            </button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th>Nama Pemohon</th>
                        <th>NPWRD</th>
                        <th>NIK</th>
                        <th>Lapak</th>
                        <th><PERSON>is <PERSON></th>
                        <th>Status</th>
                        <th>Surat</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($permohonans as $index => $item)
                    <tr id="row_{{ $item->id }}">
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $item->nama }}</td>
                        <td>{{ $item->npwrd }}</td>
                        <td>{{ $item->nik }}</td>
                        <td>
                            {{ $item->lapak->nomor ?? '-' }} - {{ $item->lapak->nama_blok ?? '-' }}
                            <br><small class="text-muted">{{ $item->lapak->pasar->nama_pasar ?? '-' }}</small>
                        </td>
                        <td>{{ $item->dagangan->nama_dagangan ?? '-' }}</td>
                        <td>
                            @if ($item->status == 'proses')
                                <span class="badge bg-warning">Proses</span>
                            @elseif($item->status == 'menunggu_verifikasi')
                                <span class="badge bg-info">Menunggu Verifikasi</span>
                            @elseif($item->status == 'diterima')
                                <span class="badge bg-success">Diterima</span>
                            @elseif($item->status == 'ditolak')
                                <span class="badge bg-danger">Ditolak</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group-vertical" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="{{ $item->id }}" data-jenis="ba_penunjukan">
                                    <i class="lni lni-download"></i> BA Penunjukan
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="{{ $item->id }}" data-jenis="sp_pemilik">
                                    <i class="lni lni-download"></i> SP Pemilik
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="{{ $item->id }}" data-jenis="surat_pernyataan">
                                    <i class="lni lni-download"></i> Surat Pernyataan
                                </button>
                            </div>
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-warning edit-btn" data-id="{{ $item->id }}">
                                <i class="lni lni-pencil"></i> Edit
                            </button>
                            <button type="button" class="btn btn-sm btn-info upload-btn" data-id="{{ $item->id }}">
                                <i class="lni lni-upload"></i> Upload
                            </button>
                            <button type="button" class="btn btn-sm btn-danger deletePermohonan"
                                data-id="{{ $item->id }}">
                                <i class="lni lni-trash"></i> Hapus
                            </button>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Permohonan -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahModalLabel">Tambah Permohonan Baru</h5>
                    <button type="button" class="btn-close addClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="lapakPermohonan" class="form-label">Pilih Lapak <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="lapakPermohonan" name="lapak_id" required>
                                        <option value="">-- Pilih Lapak --</option>
                                        @foreach ($lapaks as $lapak)
                                            <option value="{{ $lapak->id }}">
                                                {{ $lapak->nomor }} - {{ $lapak->nama_blok }}
                                                ({{ $lapak->pasar->nama_pasar }})
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="errorLapakPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="daganganPermohonan" class="form-label">Jenis Dagangan <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="daganganPermohonan" name="dagangan_id" required>
                                        <option value="">-- Pilih Jenis Dagangan --</option>
                                        @foreach ($dagangans as $dagangan)
                                            <option value="{{ $dagangan->id }}">{{ $dagangan->nama_dagangan }}</option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="errorDaganganPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="namaPermohonan" class="form-label">Nama Lengkap <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="namaPermohonan" name="nama"
                                        placeholder="Nama Lengkap" required>
                                    <div class="invalid-feedback" id="errorNamaPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nikPermohonan" class="form-label">NIK <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nikPermohonan" name="nik"
                                        placeholder="NIK" maxlength="16" required>
                                    <div class="invalid-feedback" id="errorNikPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="alamatPermohonan" class="form-label">Alamat <span
                                            class="text-danger">*</span></label>
                                    <textarea class="form-control" id="alamatPermohonan" name="alamat" rows="3" placeholder="Alamat Lengkap"
                                        required></textarea>
                                    <div class="invalid-feedback" id="errorAlamatPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="noTlpPermohonan" class="form-label">No. Telepon</label>
                                    <input type="text" class="form-control" id="noTlpPermohonan" name="no_tlp"
                                        placeholder="No. Telepon">
                                    <div class="invalid-feedback" id="errorNoTlpPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="emailPermohonan" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="emailPermohonan" name="email"
                                        placeholder="Email">
                                    <div class="invalid-feedback" id="errorEmailPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="npwrdPermohonan" class="form-label">NPWRD</label>
                                    <input type="text" class="form-control" id="npwrdPermohonan" name="npwrd"
                                        placeholder="NPWRD">
                                    <div class="invalid-feedback" id="errorNpwrdPermohonan"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm addClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Permohonan -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Edit Permohonan Baru</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <form id="editForm">
                    <input type="hidden" id="editPermohonanId" name="id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editLapakPermohonan" class="form-label">Pilih Lapak <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editLapakPermohonan" name="lapak_id" required>
                                        <option value="">-- Pilih Lapak --</option>
                                        @foreach ($lapaks as $lapak)
                                            <option value="{{ $lapak->id }}">
                                                {{ $lapak->nomor }} - {{ $lapak->nama_blok }}
                                                ({{ $lapak->pasar->nama_pasar }})
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="editErrorLapakPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editDaganganPermohonan" class="form-label">Jenis Dagangan <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editDaganganPermohonan" name="dagangan_id" required>
                                        <option value="">-- Pilih Jenis Dagangan --</option>
                                        @foreach ($dagangans as $dagangan)
                                            <option value="{{ $dagangan->id }}">{{ $dagangan->nama_dagangan }}</option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="editErrorDaganganPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNamaPermohonan" class="form-label">Nama Lengkap <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editNamaPermohonan" name="nama"
                                        placeholder="Nama Lengkap" required>
                                    <div class="invalid-feedback" id="editErrorNamaPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNikPermohonan" class="form-label">NIK <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editNikPermohonan" name="nik"
                                        placeholder="NIK" maxlength="16" required>
                                    <div class="invalid-feedback" id="editErrorNikPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="editAlamatPermohonan" class="form-label">Alamat <span
                                            class="text-danger">*</span></label>
                                    <textarea class="form-control" id="editAlamatPermohonan" name="alamat" rows="3" placeholder="Alamat Lengkap"
                                        required></textarea>
                                    <div class="invalid-feedback" id="editErrorAlamatPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editNoTlpPermohonan" class="form-label">No. Telepon</label>
                                    <input type="text" class="form-control" id="editNoTlpPermohonan" name="no_tlp"
                                        placeholder="No. Telepon">
                                    <div class="invalid-feedback" id="editErrorNoTlpPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editEmailPermohonan" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="editEmailPermohonan" name="email"
                                        placeholder="Email">
                                    <div class="invalid-feedback" id="editErrorEmailPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editNpwrdPermohonan" class="form-label">NPWRD</label>
                                    <input type="text" class="form-control" id="editNpwrdPermohonan" name="npwrd"
                                        placeholder="NPWRD">
                                    <div class="invalid-feedback" id="editErrorNpwrdPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editStatusPermohonan" class="form-label">Status <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editStatusPermohonan" name="status" required>
                                        <option value="">-- Pilih Status --</option>
                                        <option value="proses">Proses</option>
                                        <option value="menunggu_verifikasi">Menunggu Verifikasi</option>
                                        <option value="diterima">Diterima</option>
                                        <option value="ditolak">Ditolak</option>
                                    </select>
                                    <div class="invalid-feedback" id="editErrorStatusPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="editKeteranganPermohonan" class="form-label">Keterangan</label>
                                    <textarea class="form-control" id="editKeteranganPermohonan" name="keterangan" rows="2"
                                        placeholder="Keterangan (opsional)"></textarea>
                                    <div class="invalid-feedback" id="editErrorKeteranganPermohonan"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Upload Surat -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">Upload Surat & Dokumen</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="uploadForm" enctype="multipart/form-data">
                    <input type="hidden" id="uploadPermohonanId" name="permohonan_id">
                    <div class="modal-body">
                        <!-- Data Permohonan -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">Data Permohonan</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Nama:</strong> <span id="uploadNama">-</span></p>
                                        <p><strong>NIK:</strong> <span id="uploadNik">-</span></p>
                                        <p><strong>NPWRD:</strong> <span id="uploadNPWRD">-</span></p>
                                        <p><strong>Alamat:</strong> <span id="uploadAlamat">-</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Lapak:</strong> <span id="uploadLapak">-</span></p>
                                        <p><strong>Jenis Dagangan:</strong> <span id="uploadDagangan">-</span></p>
                                        <p><strong>Status:</strong> <span id="uploadStatus">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Upload Files -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Upload Dokumen</h6>
                            </div>
                            <div class="card-body">
                                <!-- Row 1 -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="fileBaPenunjukan" class="form-label">BA Penunjukan</label>
                                            <input type="file" class="form-control" id="fileBaPenunjukan"
                                                name="ba_penunjukan" accept=".jpg,.jpeg,.png">
                                            <div class="form-text">JPG, JPEG, PNG (Max: 2MB)</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="fileSpPemilik" class="form-label">SP Pemilik</label>
                                            <input type="file" class="form-control" id="fileSpPemilik"
                                                name="sp_pemilik" accept=".jpg,.jpeg,.png">
                                            <div class="form-text">JPG, JPEG, PNG (Max: 2MB)</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Row 2 -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="fileSuratPernyataan" class="form-label">Surat Pernyataan</label>
                                            <input type="file" class="form-control" id="fileSuratPernyataan"
                                                name="surat_pernyataan" accept=".jpg,.jpeg,.png">
                                            <div class="form-text">JPG, JPEG, PNG (Max: 2MB)</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="fileKtp" class="form-label">KTP</label>
                                            <input type="file" class="form-control" id="fileKtp" name="ktp"
                                                accept=".jpg,.jpeg,.png">
                                            <div class="form-text">JPG, JPEG, PNG (Max: 2MB)</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Row 3 -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="filePasFoto" class="form-label">Pas Foto</label>
                                            <input type="file" class="form-control" id="filePasFoto" name="pas_foto"
                                                accept=".jpg,.jpeg,.png">
                                            <div class="form-text">JPG, JPEG, PNG (Max: 2MB)</div>
                                        </div>
                                    </div>

                                    <!-- Checkbox disetujui -->
                                    <div class="col-md-6">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="accKepalaPasar"
                                                name="disetujui_kepala_pasar" value="1">
                                            <label class="form-check-label" for="accKepalaPasar">
                                                Disetujui oleh Kepala Pasar
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-upload"></i>
                            Upload Semua
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // Initialize Select2
                $('#lapakPermohonan, #daganganPermohonan').select2({
                    dropdownParent: $('#tambahModal'),
                    theme: 'bootstrap4'
                });

                $('#editLapakPermohonan, #editDaganganPermohonan').select2({
                    dropdownParent: $('#editModal'),
                    theme: 'bootstrap4'
                });

                // Handle form tambah permohonan
                $('#tambahForm').submit(function(e) {
                    e.preventDefault();

                    let formData = {
                        lapak_id: $('#lapakPermohonan').val(),
                        dagangan_id: $('#daganganPermohonan').val(),
                        nama: $('#namaPermohonan').val(),
                        nik: $('#nikPermohonan').val(),
                        alamat: $('#alamatPermohonan').val(),
                        no_tlp: $('#noTlpPermohonan').val(),
                        email: $('#emailPermohonan').val(),
                        npwrd: $('#npwrdPermohonan').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('permohonan.baru.store') }}',
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahForm')[0].reset();
                            $('#lapakPermohonan, #daganganPermohonan').val(null).trigger('change');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, '');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle edit button click
                $(document).on('click', '.edit-btn', function() {
                    let id = $(this).data('id');
                    $.ajax({
                        url: '{{ route('permohonan.baru.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editPermohonanId').val(response.id);
                            $('#editLapakPermohonan').val(response.lapak_id).trigger('change');
                            $('#editDaganganPermohonan').val(response.dagangan_id).trigger(
                                'change');
                            $('#editNamaPermohonan').val(response.nama);
                            $('#editNikPermohonan').val(response.nik);
                            $('#editAlamatPermohonan').val(response.alamat);
                            $('#editNoTlpPermohonan').val(response.no_tlp);
                            $('#editEmailPermohonan').val(response.email);
                            $('#editNpwrdPermohonan').val(response.npwrd);
                            $('#editStatusPermohonan').val(response.status);
                            $('#editKeteranganPermohonan').val(response.keterangan);

                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data permohonan');
                        }
                    });
                });

                // Handle form edit permohonan
                $('#editForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editPermohonanId').val();
                    let formData = {
                        lapak_id: $('#editLapakPermohonan').val(),
                        dagangan_id: $('#editDaganganPermohonan').val(),
                        nama: $('#editNamaPermohonan').val(),
                        nik: $('#editNikPermohonan').val(),
                        alamat: $('#editAlamatPermohonan').val(),
                        no_tlp: $('#editNoTlpPermohonan').val(),
                        email: $('#editEmailPermohonan').val(),
                        npwrd: $('#editNpwrdPermohonan').val(),
                        status: $('#editStatusPermohonan').val(),
                        keterangan: $('#editKeteranganPermohonan').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('permohonan.baru.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: formData,
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'edit');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle delete button click
                $(document).on('click', '.deletePermohonan', function() {
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus permohonan ini?')) {
                        $.ajax({
                            url: '{{ route('permohonan.baru.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                // Handle download button click
                $(document).on('click', '.download-btn', function() {
                    let id = $(this).data('id');
                    let jenis = $(this).data('jenis');

                    // Untuk sementara alert, nanti bisa diganti dengan download sebenarnya
                    alert('Download ' + jenis + ' untuk ID: ' + id);

                    // Uncomment untuk implementasi download sebenarnya
                    // window.open('{{ route('permohonan.baru.download-surat', [':id', ':jenis']) }}'.replace(':id', id).replace(':jenis', jenis));
                });

                // Handle upload button click
                $(document).on('click', '.upload-btn', function() {
                    let id = $(this).data('id');
                    let row = $(this).closest('tr');

                    // Get data from table row
                    let nama = row.find('td:eq(1)').text();
                    let npwrd = row.find('td:eq(2)').text();
                    let nik = row.find('td:eq(3)').text();
                    let lapakInfo = row.find('td:eq(4)').html(); 
                    let dagangan = row.find('td:eq(5)').text();
                    let statusHtml = row.find('td:eq(6)').html();

                    // Set hidden ID
                    $('#uploadPermohonanId').val(id);

                    // Fill data in modal
                    $('#uploadNama').text(nama);
                    $('#uploadNik').text(nik);
                    $('#uploadNPWRD').text(nik);
                    $('#uploadLapak').html(lapakInfo);
                    $('#uploadDagangan').text(dagangan);
                    $('#uploadStatus').html(statusHtml);

                    // Get alamat via AJAX (since it's not in table)
                    $.ajax({
                        url: '{{ route('permohonan.baru.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#uploadAlamat').text(response.alamat);
                        }
                    });

                    $('#uploadModal').modal('show');
                });

                // Handle form upload surat
                $('#uploadForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#uploadPermohonanId').val();
                    let formData = new FormData();
                    let hasFiles = false;

                    // Append all files
                    const fileInputs = [
                        'fileSpKepala', 'fileBaPenunjukan', 'fileSpPemilik',
                        'fileSuratPernyataan', 'fileKtp', 'filePasFoto'
                    ];

                    fileInputs.forEach(function(inputId) {
                        const fileInput = $('#' + inputId)[0];
                        if (fileInput.files.length > 0) {
                            formData.append(inputId.replace('file', '').toLowerCase(), fileInput.files[
                                0]);
                            hasFiles = true;
                        }
                    });

                    if (!hasFiles) {
                        alert('Pilih minimal satu file untuk diupload');
                        return;
                    }

                    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

                    // Reset validation
                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('permohonan.baru.update', ':id') }}'.replace(':id', id),
                        method: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        headers: {
                            'X-HTTP-Method-Override': 'PUT'
                        },
                        success: function(response) {
                            $('#uploadModal').modal('hide');
                            $('#uploadForm')[0].reset();
                            round_success_noti('File berhasil diupload');
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                Object.keys(errors).forEach(function(key) {
                                    const inputId = 'file' + key.charAt(0).toUpperCase() +
                                        key.slice(1);
                                    $('#' + inputId).addClass('is-invalid');
                                });
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle validation errors
                function handleValidationErrors(errors, prefix) {
                    const fieldMap = {
                        'lapak_id': prefix + 'LapakPermohonan',
                        'dagangan_id': prefix + 'DaganganPermohonan',
                        'nama': prefix + 'NamaPermohonan',
                        'nik': prefix + 'NikPermohonan',
                        'alamat': prefix + 'AlamatPermohonan',
                        'no_tlp': prefix + 'NoTlpPermohonan',
                        'email': prefix + 'EmailPermohonan',
                        'npwrd': prefix + 'NpwrdPermohonan',
                        'status': prefix + 'StatusPermohonan',
                        'keterangan': prefix + 'KeteranganPermohonan'
                    };

                    Object.keys(errors).forEach(function(key) {
                        if (fieldMap[key]) {
                            $('#' + fieldMap[key]).addClass('is-invalid');
                            let errorId = prefix ? 'editError' + fieldMap[key].replace(prefix, '') : 'error' +
                                fieldMap[key];
                            $('#' + errorId).text(errors[key][0]);
                        }
                    });
                }

                // Handle modal close events
                $('.addClose, .editClose').on('click', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                $('#tambahModal, #editModal, #uploadModal').on('hidden.bs.modal', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                function loadTableData() {
                    location.reload();
                }
            });
        </script>
    @endpush
@endsection
