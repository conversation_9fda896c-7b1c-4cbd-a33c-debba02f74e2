@extends('layouts.app')

@section('title', 'Permohonan Baru')

@push('styles')
    <style>
        .filepond--credits {
            display: none !important;
        }

        .preview-img-wrapper img {
            transition: transform 0.2s ease;
            cursor: zoom-in;
        }
    </style>
@endpush

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Permohonan">
            <x-breadcrumb-item>Permohonan Baru</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal">
                <i class="lni lni-plus"></i> Tambah Permohonan
            </button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th><PERSON><PERSON></th>
                        <th>NPWRD</th>
                        <th>NIK</th>
                        <th><PERSON><PERSON>k</th>
                        <th><PERSON><PERSON></th>
                        <th>Status</th>
                        <th>Surat</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($permohonans as $item)
                    <tr id="row_{{ $item->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->nama }}</td>
                        <td>{{ $item->npwrd }}</td>
                        <td>{{ $item->nik }}</td>
                        <td>
                            {{ $item->lapak->nomor ?? '-' }} - {{ $item->lapak->nama_blok ?? '-' }}
                            <br><small class="text-muted">{{ $item->lapak->pasar->nama_pasar ?? '-' }}</small>
                        </td>
                        <td>{{ $item->dagangan->nama_dagangan ?? '-' }}</td>
                        <td>
                            @if ($item->status == 'proses')
                                <span class="badge bg-warning">Proses</span>
                            @elseif($item->status == 'menunggu_verifikasi')
                                <span class="badge bg-info">Menunggu Verifikasi</span>
                            @elseif($item->status == 'diterima')
                                <span class="badge bg-success">Diterima</span>
                            @elseif($item->status == 'ditolak')
                                <span class="badge bg-danger">Ditolak</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group-vertical" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="{{ $item->id }}" data-jenis="ba_penunjukan">
                                    <i class="lni lni-download"></i> BA Penunjukan
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="{{ $item->id }}" data-jenis="sp_pemilik">
                                    <i class="lni lni-download"></i> SP Pemilik
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="{{ $item->id }}" data-jenis="surat_pernyataan">
                                    <i class="lni lni-download"></i> Surat Pernyataan
                                </button>
                            </div>
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-warning edit-btn" data-id="{{ $item->id }}">
                                <i class="lni lni-pencil"></i> Edit
                            </button>
                            <button type="button" class="btn btn-sm btn-info upload-btn" data-id="{{ $item->id }}">
                                <i class="lni lni-upload"></i> Upload
                            </button>
                            <button type="button" class="btn btn-sm btn-danger deletePermohonan"
                                data-id="{{ $item->id }}">
                                <i class="lni lni-trash"></i> Hapus
                            </button>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Permohonan -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahModalLabel">Tambah Permohonan Baru</h5>
                    <button type="button" class="btn-close addClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="lapakPermohonan" class="form-label">Pilih Lapak <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="lapakPermohonan" name="lapak_id">
                                        <option value="">-- Pilih Lapak --</option>
                                        @foreach ($lapaks as $lapak)
                                            <option value="{{ $lapak->id }}">
                                                {{ $lapak->nomor }} - {{ $lapak->nama_blok }}
                                                ({{ $lapak->pasar->nama_pasar }})
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="errorLapakPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="daganganPermohonan" class="form-label">Jenis Dagangan <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="daganganPermohonan" name="dagangan_id">
                                        <option value="">-- Pilih Jenis Dagangan --</option>
                                        @foreach ($dagangans as $dagangan)
                                            <option value="{{ $dagangan->id }}">{{ $dagangan->nama_dagangan }}</option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="errorDaganganPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="namaPermohonan" class="form-label">Nama Lengkap <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="namaPermohonan" name="nama"
                                        placeholder="Nama Lengkap">
                                    <div class="invalid-feedback" id="errorNamaPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nikPermohonan" class="form-label">NIK <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nikPermohonan" name="nik"
                                        placeholder="NIK" maxlength="16">
                                    <div class="invalid-feedback" id="errorNikPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="alamatPermohonan" class="form-label">Alamat <span
                                            class="text-danger">*</span></label>
                                    <textarea class="form-control" id="alamatPermohonan" name="alamat" rows="3" placeholder="Alamat Lengkap"></textarea>
                                    <div class="invalid-feedback" id="errorAlamatPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="noTlpPermohonan" class="form-label">No. Telepon</label>
                                    <input type="text" class="form-control" id="noTlpPermohonan" name="no_tlp"
                                        placeholder="No. Telepon">
                                    <div class="invalid-feedback" id="errorNoTlpPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="emailPermohonan" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="emailPermohonan" name="email"
                                        placeholder="Email">
                                    <div class="invalid-feedback" id="errorEmailPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="npwrdPermohonan" class="form-label">NPWRD</label>
                                    <input type="text" class="form-control" id="npwrdPermohonan" name="npwrd"
                                        placeholder="NPWRD">
                                    <div class="invalid-feedback" id="errorNpwrdPermohonan"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm addClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Permohonan -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Edit Permohonan Baru</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <form id="editForm">
                    <input type="hidden" id="editPermohonanId" name="id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editLapakPermohonan" class="form-label">Pilih Lapak <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editLapakPermohonan" name="lapak_id">
                                        <option value="">-- Pilih Lapak --</option>
                                        @foreach ($lapaks as $lapak)
                                            <option value="{{ $lapak->id }}">
                                                {{ $lapak->nomor }} - {{ $lapak->nama_blok }}
                                                ({{ $lapak->pasar->nama_pasar }})
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="editErrorLapakPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editDaganganPermohonan" class="form-label">Jenis Dagangan <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editDaganganPermohonan" name="dagangan_id">
                                        <option value="">-- Pilih Jenis Dagangan --</option>
                                        @foreach ($dagangans as $dagangan)
                                            <option value="{{ $dagangan->id }}">{{ $dagangan->nama_dagangan }}</option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="editErrorDaganganPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNamaPermohonan" class="form-label">Nama Lengkap <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editNamaPermohonan" name="nama"
                                        placeholder="Nama Lengkap">
                                    <div class="invalid-feedback" id="editErrorNamaPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNikPermohonan" class="form-label">NIK <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editNikPermohonan" name="nik"
                                        placeholder="NIK" maxlength="16">
                                    <div class="invalid-feedback" id="editErrorNikPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="editAlamatPermohonan" class="form-label">Alamat <span
                                            class="text-danger">*</span></label>
                                    <textarea class="form-control" id="editAlamatPermohonan" name="alamat" rows="3"
                                        placeholder="Alamat Lengkap"></textarea>
                                    <div class="invalid-feedback" id="editErrorAlamatPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editNoTlpPermohonan" class="form-label">No. Telepon</label>
                                    <input type="text" class="form-control" id="editNoTlpPermohonan" name="no_tlp"
                                        placeholder="No. Telepon">
                                    <div class="invalid-feedback" id="editErrorNoTlpPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editEmailPermohonan" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="editEmailPermohonan" name="email"
                                        placeholder="Email">
                                    <div class="invalid-feedback" id="editErrorEmailPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editNpwrdPermohonan" class="form-label">NPWRD</label>
                                    <input type="text" class="form-control" id="editNpwrdPermohonan" name="npwrd"
                                        placeholder="NPWRD">
                                    <div class="invalid-feedback" id="editErrorNpwrdPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editStatusPermohonan" class="form-label">Status <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editStatusPermohonan" name="status">
                                        <option value="">-- Pilih Status --</option>
                                        <option value="proses">Proses</option>
                                        <option value="menunggu_verifikasi">Menunggu Verifikasi</option>
                                        <option value="diterima">Diterima</option>
                                        <option value="ditolak">Ditolak</option>
                                    </select>
                                    <div class="invalid-feedback" id="editErrorStatusPermohonan"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Upload Surat -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">Upload Surat</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="uploadForm" enctype="multipart/form-data">
                    <input type="hidden" id="uploadPermohonanId" name="permohonan_id">
                    <input type="hidden" name="persetujuan_kepala_pasar" value="0">
                    <div class="modal-body">
                        <div class="mb-3">
                            <h6 class="mb-0">Data Permohonan</h6>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Nama:</strong> <span id="uploadNama">-</span></p>
                                        <p><strong>NIK:</strong> <span id="uploadNik">-</span></p>
                                        <p><strong>NPWRD:</strong> <span id="uploadNPWRD">-</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Lapak:</strong> <span id="uploadLapak">-</span></p>
                                        <p><strong>Jenis Dagangan:</strong> <span id="uploadDagangan">-</span></p>
                                        <p><strong>Alamat:</strong> <span id="uploadAlamat">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h6 class="mb-3">Dokumen Permohonan</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="ba_penunjukan" class="form-label">BA Penunjukan</label>
                                <div id="preview_ba_penunjukan" class="mb-2"></div>
                                <input type="file" class="filepond" id="ba_penunjukan" name="ba_penunjukan"
                                    accept="image/*" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="sp_pemilik" class="form-label">SP Pemilik</label>
                                <div id="preview_sp_pemilik" class="mb-2"></div>
                                <input type="file" class="filepond" id="sp_pemilik" name="sp_pemilik"
                                    accept="image/*" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="surat_pernyataan" class="form-label">Surat Pernyataan</label>
                                <div id="preview_surat_pernyataan" class="mb-2"></div>
                                <input type="file" class="filepond" id="surat_pernyataan" name="surat_pernyataan"
                                    accept="image/*" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="ktp" class="form-label">KTP</label>
                                <div id="preview_ktp" class="mb-2"></div>
                                <input type="file" class="filepond" id="ktp" name="ktp"
                                    accept="image/*" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="pas_foto" class="form-label">Pas Foto</label>
                                <div id="preview_pas_foto" class="mb-2"></div>
                                <input type="file" class="filepond" id="pas_foto" name="pas_foto"
                                    accept="image/*" />
                            </div>

                            <div class="mb-3">
                                <label class="form-label d-block mb-1" for="keterangan">Keterangan</label>
                                <textarea class="form-control" id="uploadKeterangan" name="keterangan" rows="2"
                                    placeholder="Keterangan (opsional)"></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label d-block mb-1" for="persetujuan_kepala_pasar">Disetujui Kepala
                                    Pasar</label>
                                <div class="form-control d-flex align-items-center" style="height: 48px;">
                                    <input type="checkbox" class="form-check-input me-2" id="persetujuanKepalaPasar"
                                        name="persetujuan_kepala_pasar">
                                    <span class="text-muted">Centang jika disetujui</span>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i> Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-upload"></i> Upload
                        </button>
                    </div>
                </form>

            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // Initialize Select2
                $('#lapakPermohonan, #daganganPermohonan').select2({
                    dropdownParent: $('#tambahModal'),
                    theme: 'bootstrap4'
                });

                $('#editLapakPermohonan, #editDaganganPermohonan').select2({
                    dropdownParent: $('#editModal'),
                    theme: 'bootstrap4'
                });

                // Handle form tambah permohonan
                $('#tambahForm').submit(function(e) {
                    e.preventDefault();

                    let formData = {
                        lapak_id: $('#lapakPermohonan').val(),
                        dagangan_id: $('#daganganPermohonan').val(),
                        nama: $('#namaPermohonan').val(),
                        nik: $('#nikPermohonan').val(),
                        alamat: $('#alamatPermohonan').val(),
                        no_tlp: $('#noTlpPermohonan').val(),
                        email: $('#emailPermohonan').val(),
                        npwrd: $('#npwrdPermohonan').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('permohonan.baru.store') }}',
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahForm')[0].reset();
                            $('#lapakPermohonan, #daganganPermohonan').val(null).trigger('change');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, '');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle edit button click
                $(document).on('click', '.edit-btn', function() {
                    let id = $(this).data('id');
                    $.ajax({
                        url: '{{ route('permohonan.baru.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editPermohonanId').val(response.id);
                            $('#editLapakPermohonan').val(response.lapak_id).trigger('change');
                            $('#editDaganganPermohonan').val(response.dagangan_id).trigger(
                                'change');
                            $('#editNamaPermohonan').val(response.nama);
                            $('#editNikPermohonan').val(response.nik);
                            $('#editAlamatPermohonan').val(response.alamat);
                            $('#editNoTlpPermohonan').val(response.no_tlp);
                            $('#editEmailPermohonan').val(response.email);
                            $('#editNpwrdPermohonan').val(response.npwrd);
                            $('#editStatusPermohonan').val(response.status);
                            $('#editKeteranganPermohonan').val(response.keterangan);

                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data permohonan');
                        }
                    });
                });

                // Handle form edit permohonan
                $('#editForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editPermohonanId').val();
                    let formData = {
                        lapak_id: $('#editLapakPermohonan').val(),
                        dagangan_id: $('#editDaganganPermohonan').val(),
                        nama: $('#editNamaPermohonan').val(),
                        nik: $('#editNikPermohonan').val(),
                        alamat: $('#editAlamatPermohonan').val(),
                        no_tlp: $('#editNoTlpPermohonan').val(),
                        email: $('#editEmailPermohonan').val(),
                        npwrd: $('#editNpwrdPermohonan').val(),
                        status: $('#editStatusPermohonan').val(),
                        keterangan: $('#editKeteranganPermohonan').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('permohonan.baru.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: formData,
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'edit');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle delete button click
                $(document).on('click', '.deletePermohonan', function() {
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus permohonan ini?')) {
                        $.ajax({
                            url: '{{ route('permohonan.baru.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                const routeDownload = '{{ route('permohonan.baru.downlaod.dokumen', [':id', ':jenis']) }}';

                $(document).on('click', '.download-btn', function() {
                    let id = $(this).data('id');
                    let jenis = $(this).data('jenis');

                    let url = routeDownload.replace(':id', id).replace(':jenis', jenis);

                    window.open(url, '_blank');
                });


                // Handle validation errors
                function handleValidationErrors(errors, prefix) {
                    const fieldMap = {
                        'lapak_id': prefix + 'LapakPermohonan',
                        'dagangan_id': prefix + 'DaganganPermohonan',
                        'nama': prefix + 'NamaPermohonan',
                        'nik': prefix + 'NikPermohonan',
                        'alamat': prefix + 'AlamatPermohonan',
                        'no_tlp': prefix + 'NoTlpPermohonan',
                        'email': prefix + 'EmailPermohonan',
                        'npwrd': prefix + 'NpwrdPermohonan',
                        'status': prefix + 'StatusPermohonan',
                        'keterangan': prefix + 'KeteranganPermohonan'
                    };

                    Object.keys(errors).forEach(function(key) {
                        if (fieldMap[key]) {
                            let inputId = fieldMap[key].charAt(0).toLowerCase() + fieldMap[key].slice(1);
                            $('#' + inputId).addClass('is-invalid');

                            let errorId = prefix ? 'editError' + fieldMap[key].replace(prefix, '') : 'error' +
                                fieldMap[key];
                            $('#' + errorId).text(errors[key][0]);
                        }
                    });
                }

                // Handle modal close events
                $('.addClose, .editClose').on('click', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                $('#tambahModal, #editModal, #uploadModal').on('hidden.bs.modal', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    ['ba_penunjukan', 'sp_pemilik', 'surat_pernyataan', 'ktp', 'pas_foto'].forEach(field => {
                        $(this).find(`#preview_${field}`).empty().removeClass('terisi');
                    });

                });


                function loadTableData() {
                    $.ajax({
                        url: '{{ route('permohonan.baru.index') }}',
                        method: 'GET',
                        success: function(response) {
                            let newTableBody = $(response).find('tbody').html();
                            $('tbody').html(newTableBody);
                        },
                        error: function() {
                            console.log('Gagal memuat ulang data tabel');
                        }
                    });
                }


                $(document).on('click', '.upload-btn', function() {
                    let id = $(this).data('id');
                    initializeFilePond();
                    $.ajax({
                        url: '{{ route('permohonan.baru.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            // Isi data teks
                            $('#uploadPermohonanId').val(response.id);
                            $('#uploadNama').text(response.nama);
                            $('#uploadNik').text(response.nik);
                            $('#uploadAlamat').text(response.alamat);
                            $('#uploadLapak').text(response.lapak.jenis + ' - ' + response.lapak
                                .nomor + ' - ' + response.lapak.nama_blok + ' - ' + response
                                .lapak.pasar.nama_pasar);
                            $('#uploadDagangan').text(response.dagangan.nama_dagangan);
                            $('#uploadNPWRD').text(response.npwrd);
                            $('#uploadKeterangan').val(response.keterangan);
                            $('#persetujuanKepalaPasar').prop('checked', response
                                .persetujuan_kepala_pasar);

                            const dokumenFields = [
                                'ba_penunjukan',
                                'sp_pemilik',
                                'surat_pernyataan',
                                'ktp',
                                'pas_foto'
                            ];

                            dokumenFields.forEach(field => {
                                const url = response[`${field}_url`];
                                const container = $(`#preview_${field}`);

                                if (url) {
                                    container.html(`
                                    <div class="d-flex justify-content-center mb-2">
                                        <a href="${url}" target="_blank">
                                            <img src="${url}" class="img-fluid rounded border" style="max-height: 150px;">
                                        </a>
                                    </div>
                                `).addClass('terisi');;
                                } else {
                                    container.empty().removeClass('terisi');;
                                }
                            });
                            initModal();

                            $('#uploadModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data permohonan');
                        }
                    });
                });

                // Handle form upload surat
                $('#uploadForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#uploadPermohonanId').val();
                    let form = $('#uploadForm')[0];
                    let formData = new FormData(form);

                    if (!$('#persetujuanKepalaPasar').is(':checked')) {
                        formData.set('persetujuan_kepala_pasar', 0);
                    }

                    // Reset error
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('permohonan.baru.upload', ':id') }}'.replace(':id', id),
                        method: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#uploadModal').modal('hide');
                            round_success_noti(response.success || 'Dokumen berhasil diupload');
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'upload');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });
            });
        </script>
        <script src="{{ asset('assets/js/app/permohonan-baru.js') }}"></script>
    @endpush
@endsection
