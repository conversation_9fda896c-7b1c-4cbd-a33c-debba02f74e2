@extends('layouts.app')

@section('title', 'Permohonan Baru')

@push('styles')
    <style>
        .filepond--credits {
            display: none !important;
        }

        .preview-img-wrapper img {
            transition: transform 0.2s ease;
            cursor: zoom-in;
        }
    </style>
@endpush

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Permohonan">
            <x-breadcrumb-item>Permohonan Baru</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal">
                <i class="lni lni-plus"></i> Tambah Permohonan
            </button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th><PERSON><PERSON></th>
                        <th>NPWRD</th>
                        <th>NIK</th>
                        <th><PERSON><PERSON>k</th>
                        <th><PERSON><PERSON></th>
                        <th>Status</th>
                        <th>Dokumen</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($permohonans as $item)
                    <tr id="row_{{ $item->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->nama }}</td>
                        <td>{{ $item->npwrd }}</td>
                        <td>{{ $item->nik }}</td>
                        <td>
                            {{ $item->lapak->nomor ?? '-' }} - {{ $item->lapak->nama_blok ?? '-' }}
                            <br><small class="text-muted">{{ $item->lapak->pasar->nama_pasar ?? '-' }}</small>
                        </td>
                        <td>{{ $item->dagangan->nama_dagangan ?? '-' }}</td>
                        <td>
                            @if ($item->status == 'proses')
                                <span class="badge bg-warning">Proses</span>
                            @elseif($item->status == 'menunggu_verifikasi')
                                <span class="badge bg-info">Menunggu Verifikasi</span>
                            @elseif($item->status == 'diterima')
                                <span class="badge bg-success">Diterima</span>
                            @elseif($item->status == 'ditolak')
                                <span class="badge bg-danger">Ditolak</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group-vertical" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="{{ $item->id }}" data-jenis="ba_penunjukan">
                                    <i class="lni lni-download"></i> BA Penunjukan
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="{{ $item->id }}" data-jenis="sp_pemilik">
                                    <i class="lni lni-download"></i> SP Pemilik
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="{{ $item->id }}" data-jenis="surat_pernyataan">
                                    <i class="lni lni-download"></i> Surat Pernyataan
                                </button>
                            </div>
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-warning edit-btn" data-id="{{ $item->id }}">
                                <i class="lni lni-pencil"></i> Edit
                            </button>
                            <button type="button" class="btn btn-sm btn-info upload-btn" data-id="{{ $item->id }}">
                                <i class="lni lni-upload"></i> Upload
                            </button>
                            <button type="button" class="btn btn-sm btn-danger deletePermohonan"
                                data-id="{{ $item->id }}">
                                <i class="lni lni-trash"></i> Hapus
                            </button>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Permohonan -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Tambah Permohonan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Tutup"></button>
                </div>
                <form id="formPermohonan">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="lapakSelect" class="form-label">Pilih Lapak</label>
                            <select class="form-select" id="lapakSelect" name="lapak_id" required>
                                <option value="">-- Pilih Lapak --</option>
                                @foreach ($objekRetribusi as $item)
                                    <option value="{{ $item->id }}">
                                        {{ $item->lapak->jenis }} - {{ $item->lapak->nama_blok }} - {{ $item->lapak->nomor }}
                                        ({{ $item->pasar->nama_pasar ?? '-' }})
                                    </option>
                                @endforeach
                                <div class="invalid-feedback" id="errorLapakSelect"></div>
                            </select>
                        </div>

                        <!-- Otomatis terisi -->
                        <div class="mb-3">
                            <label for="npwrd" class="form-label">NPWRD</label>
                            <input type="text" class="form-control" id="npwrd" name="npwrd" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="nama" class="form-label">Nama Pemilik</label>
                            <input type="text" class="form-control" id="nama" name="nama" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="nik" class="form-label">NIK</label>
                            <input type="text" class="form-control" id="nik" name="nik" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="dagangan" class="form-label">
                                <input type="text" class="form-control" id="dagangan" name="dagangan" readonly>
                            </label>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        $(document).ready(function() {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            $('#lapakSelect').on('change', function(e) {
                e.preventDefault();
                let lapakId = $(this).val();
                $.ajax({
                    url: '{{ route('permohonan.perpanjang.getLapak') }}',
                    method: 'POST',
                    data: {
                        lapak_id: lapakId,
                    },
                    success: function(response) {
                        $('#npwrd').val(response.npwrd);
                        $('#nama').val(response.nama);
                        $('#nik').val(response.nik);
                        $('#dagangan').val(response.dagangan);
                    },
                    error: function() {
                        alert('Gagal mengambil data lapak');
                    }
                });
            });
        });
    </script>
    @endpush
@endsection
