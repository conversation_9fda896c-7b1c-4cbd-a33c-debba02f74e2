@extends('layouts.app')

@section('title', 'Data Objek Retribusi')

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Data Retribusi">
            <x-breadcrumb-item>Objek Retribusi</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal"><i
                    class="lni lni-plus"></i> Tambah</button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th>Nama Wajib Retribusi</th>
                        <th>Nama <PERSON></th>
                        <th>Lapak</th>
                        <th>Luas</th>
                        <th>Nama Blok</th>
                        <th>Nomor Blok</th>
                        <th>Tanggal Daftar</th>
                        <th><PERSON>as Berlaku</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($objekRetribusi as $item)
                    <tr id="row_{{ $item->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->WajibRetribusi->nama }}</td>
                        <td>{{ $item->lapak->pasar->nama_pasar }}</td>
                        <td>{{ ucFirst($item->lapak->jenis) }} </td>
                        <td>{{ $item->lapak->lebar }}M x {{ $item->lapak->panjang }}M</td>
                        <td>{{ $item->lapak->nama_blok }}</td>
                        <td>{{ $item->lapak->nomor }}</td>
                        <td>{{ $item->tgl_daftar }}</td>
                        <td>{{ $item->batas_berlaku }}</td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <i class="lni lni-more-alt"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item edit-btn" href="" data-id="{{ $item->id }}" data-nama="{{ $item->wajibRetribusi->nama }}">
                                            <i class="lni lni-pencil me-2"></i>Edit
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item text-danger delete-btn" href=""
                                            data-id="{{ $item->id }}">
                                            <i class="lni lni-trash me-2"></i>Hapus
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Tambah Objek Retribusi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahObjekForm">
                    <div class="modal-body">

                        <!-- Pilih NPWRD -->
                        <div class="mb-3">
                            <label for="wajibRetribusi" class="form-label">Pilih NPWRD <span
                                    class="text-danger">*</span></label>
                            <select class="form-select" id="wajibRetribusi" name="wajib_retribusi_id">
                                <option value="">-- Pilih NPWRD --</option>
                                @foreach ($wajibRetribusiList as $wr)
                                    <option value="{{ $wr->id }}">{{ $wr->npwrd }}</option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback" id="errorWajibRetribusi"></div>
                        </div>

                        <!-- Nama Otomatis -->
                        <div class="mb-3">
                            <label for="namaWajibRetribusi" class="form-label">Nama Wajib Retribusi</label>
                            <input type="text" class="form-control" id="namaWajibRetribusi" disabled>
                        </div>

                        <!-- Pilih Dagangan -->
                        <div class="mb-3">
                            <label for="dagangan" class="form-label">Pilih Dagangan <span
                                    class="text-danger">*</span></label>
                            <select class="form-select" id="dagangan" name="dagangan_id">
                                <option value="">-- Pilih Dagangan --</option>
                                @foreach ($daganganList as $dagangan)
                                    <option value="{{ $dagangan->id }}">{{ $dagangan->nama_dagangan }}</option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback" id="errorDagangan"></div>
                        </div>

                        <!-- Pilih Lapak -->
                        <div class="mb-3">
                            <label for="lapak" class="form-label">Pilih Lapak <span class="text-danger">*</span></label>
                            <select class="form-select" id="lapak" name="lapak_id">
                                <option value="">-- Pilih Lapak --</option>
                                @foreach ($lapakList as $lapak)
                                    <option value="{{ $lapak->id }}">
                                        [{{ strtoupper($lapak->jenis) }}] - Blok {{ $lapak->nama_blok }}
                                        No. {{ $lapak->nomor }}
                                        @if ($lapak->pasar)
                                            - {{ $lapak->pasar->nama_pasar }}
                                        @endif
                                    </option>
                                @endforeach
                            </select>

                            <div class="invalid-feedback" id="errorLapak"></div>
                        </div>

                        <!-- Tanggal Daftar -->
                        <div class="mb-3">
                            <label for="tglDaftar" class="form-label">Tanggal Daftar <span
                                    class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="tglDaftar" name="tglDaftar">
                            <div class="invalid-feedback" id="errorTglDaftar"></div>
                        </div>

                        <!-- Batas Berlaku -->
                        <div class="mb-3">
                            <label for="batas_berlaku" class="form-label">Batas Berlaku <span
                                    class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="batasBerlaku" name="batasBerlaku">
                            <div class="invalid-feedback" id="errorBatasBerlaku"></div>
                        </div>

                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i> Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i> Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Objek Retribusi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Tutup"></button>
            </div>
            <form id="editObjekForm">
                <input type="hidden" id="editId" name="id">
                <div class="modal-body">

                    <!-- Pilih NPWRD -->
                    <div class="mb-3">
                        <label for="editWajibRetribusi" class="form-label">Pilih NPWRD <span class="text-danger">*</span></label>
                        <select class="form-select" id="editWajibRetribusi" name="wajib_retribusi_id">
                            <option value="">-- Pilih NPWRD --</option>
                            @foreach ($wajibRetribusiList as $wr)
                                <option value="{{ $wr->id }}">{{ $wr->npwrd }}</option>
                            @endforeach
                        </select>
                        <div class="invalid-feedback" id="editErrorWajibRetribusi"></div>
                    </div>

                    <!-- Nama Otomatis -->
                    <div class="mb-3">
                        <label for="editNamaWajibRetribusi" class="form-label">Nama Wajib Retribusi</label>
                        <input type="text" class="form-control" id="editNamaWajibRetribusi" disabled>
                    </div>

                    <!-- Pilih Dagangan -->
                    <div class="mb-3">
                        <label for="editDagangan" class="form-label">Pilih Dagangan <span class="text-danger">*</span></label>
                        <select class="form-select" id="editDagangan" name="dagangan_id">
                            <option value="">-- Pilih Dagangan --</option>
                            @foreach ($daganganList as $dagangan)
                                <option value="{{ $dagangan->id }}">{{ $dagangan->nama_dagangan }}</option>
                            @endforeach
                        </select>
                        <div class="invalid-feedback" id="editErrorDagangan"></div>
                    </div>

                    <!-- Pilih Lapak -->
                    <div class="mb-3">
                        <label for="editLapak" class="form-label">Pilih Lapak <span class="text-danger">*</span></label>
                        <select class="form-select" id="editLapak" name="lapak_id">
                            <option value="">-- Pilih Lapak --</option>
                            @foreach ($lapakList as $lapak)
                                <option value="{{ $lapak->id }}">
                                    [{{ strtoupper($lapak->jenis) }}] - Blok {{ $lapak->nama_blok }} No. {{ $lapak->nomor }}
                                    @if ($lapak->pasar)
                                        - {{ $lapak->pasar->nama_pasar }}
                                    @endif
                                </option>
                            @endforeach
                        </select>
                        <div class="invalid-feedback" id="editErrorLapak"></div>
                    </div>

                    <!-- Tanggal Daftar -->
                    <div class="mb-3">
                        <label for="editTglDaftar" class="form-label">Tanggal Daftar <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="editTglDaftar" name="tglDaftar">
                        <div class="invalid-feedback" id="editErrorTglDaftar"></div>
                    </div>

                    <!-- Batas Berlaku -->
                    <div class="mb-3">
                        <label for="editBatasBerlaku" class="form-label">Batas Berlaku <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="editBatasBerlaku" name="batasBerlaku">
                        <div class="invalid-feedback" id="editErrorBatasBerlaku"></div>
                    </div>

                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                        <i class="lni lni-close"></i> Tutup
                    </button>
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="lni lni-save"></i> Update
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                

                $('#wajibRetribusi').on('change', function(e) {
                    e.preventDefault();
                    let wajibRetribusiId = $(this).val();
                    $.ajax({
                        url: '{{ route('retribusi.wajib.edit', ':id') }}'.replace(':id',
                            wajibRetribusiId),
                        method: 'GET',
                        data: wajibRetribusiId,
                        success: function(response) {
                            $('#namaWajibRetribusi').val(response.nama);
                        },
                        error: function() {
                            alert('Gagal mengambil data wajib retribusi');
                        }
                    });
                });

                $('#tambahObjekForm').submit(function(e) {
                    e.preventDefault();
                    let formData = {
                        wajib_retribusi_id: $('#wajibRetribusi').val(),
                        dagangan_id: $('#dagangan').val(),
                        lapak_id: $('#lapak').val(),
                        tgl_daftar: $('#tglDaftar').val(),
                        batas_berlaku: $('#batasBerlaku').val()
                    };
                    $.ajax({
                        url: '{{ route('retribusi.objek.store') }}',
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahObjekForm')[0].reset();
                            round_success_noti(response.success);
                            loadTableData();
                            $('#wajibRetribusi, #dagangan, #lapak').val(null).trigger('change');
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, '');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });

                });

                $('.edit-btn').on('click',function(e){
                    e.preventDefault();
                    let id = $(this).data('id');
                    let nama = $(this).data('nama');
                    $.ajax({
                        url: '{{ route('retribusi.objek.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editId').val(response.id);
                            $('#editWajibRetribusi').val(response.wajib_retribusi_id).trigger('change');
                            $('#editNamaWajibRetribusi').val(nama);
                            $('#editDagangan').val(response.dagangan_id).trigger('change');
                            $('#editLapak').val(response.lapak_id).trigger('change');
                            $('#editTglDaftar').val(response.tgl_daftar);
                            $('#editBatasBerlaku').val(response.batas_berlaku);
                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data objek retribusi');
                        }
                    });
                });

                $('#editObjekForm').submit(function(e) {
                    e.preventDefault();
                    let formData = {
                        wajib_retribusi_id: $('#editWajibRetribusi').val(),
                        dagangan_id: $('#editDagangan').val(),
                        lapak_id: $('#editLapak').val(),
                        tgl_daftar: $('#editTglDaftar').val(),
                        batas_berlaku: $('#editBatasBerlaku').val()
                    };
                    let id = $('#editId').val();
                    $.ajax({
                        url: '{{ route('retribusi.objek.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: formData,
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'edit');
                                console.log(errors);
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                $(document).on('click', '.delete-btn', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');
                    if (confirm('Apakah Anda yakin ingin menghapus data objek retribusi ini?')) {
                        $.ajax({
                            url: '{{ route('retribusi.objek.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                function handleValidationErrors(errors, prefix) {
                    const fieldMap = {
                        'wajib_retribusi_id': prefix + 'WajibRetribusi',
                        'dagangan_id': prefix + 'Dagangan',
                        'lapak_id': prefix + 'Lapak',
                        'tgl_daftar': prefix + 'TglDaftar',
                        'batas_berlaku': prefix + 'BatasBerlaku'
                    };
                    console.log(fieldMap);
                    Object.keys(errors).forEach(function(key) {
                        if (fieldMap[key]) {
                            let inputId = fieldMap[key].charAt(0).toLowerCase() + fieldMap[key].slice(1);
                            $('#' + inputId).addClass('is-invalid');
                            let errorId = prefix ? 'editError' + fieldMap[key].replace(prefix, '') : 'error' +
                                fieldMap[key];

                            $('#' + errorId).text(errors[key][0]);
                        }
                    });
                }

                function loadTableData() {
                    $.ajax({
                        url: '{{ route('retribusi.objek.index') }}',
                        method: 'GET',
                        success: function(response) {
                            let newTableBody = $(response).find('tbody').html();
                            $('tbody').html(newTableBody);
                        },
                        error: function() {
                            console.log('Error loading table data');
                        }
                    });
                }

                $('#wajibRetribusi, #dagangan, #lapak').select2({
                    dropdownParent: $('#tambahModal .modal-content'),
                    theme: 'bootstrap4'
                });

                $('#editWajibRetribusi, #editDagangan, #editLapak').select2({
                    dropdownParent: $('#editModal .modal-content'),
                    theme: 'bootstrap4'
                })

                $('.addClose, .editClose').on('click', function() {
                    clearValidation('#tambahForm');
                    clearValidation('#editForm');
                });

                $('#tambahModal').on('hidden.bs.modal', function() {
                    clearValidation('#tambahForm');
                });

                $('#editModal').on('hidden.bs.modal', function() {
                    clearValidation('#editForm');
                });

                function clearValidation(formId) {
                    $(formId).find('input, select').removeClass('is-invalid');
                    $(formId).find('.invalid-feedback').text('');
                    $(formId)[0].reset();
                }


            });
        </script>
    @endpush

@endsection
