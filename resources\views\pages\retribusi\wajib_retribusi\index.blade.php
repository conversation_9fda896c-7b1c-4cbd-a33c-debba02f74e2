@extends('layouts.app')

@section('title', 'Data Wajib Retribusi')

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Data Retribusi">
            <x-breadcrumb-item>Wajib Retribusi</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal"><i
                    class="lni lni-plus"></i> Tambah</button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th>NPWRD</th>
                        <th>Nama</th>
                        <th>NIK</th>
                        <th>Alamat</th>
                        <th>Nomor Telephone</th>
                        <th>Email</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($wajibRetribusi as $item)
                    <tr id="row_{{ $item->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->npwrd ?? '-' }}</td>
                        <td>{{ $item->nama }}</td>
                        <td>{{ $item->nik }}</td>
                        <td>{{ $item->alamat }}</td>
                        <td>{{ $item->no_telp ?? '-' }}</td>
                        <td>{{ $item->email ?? '-' }}</td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <i class="lni lni-more-alt"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item edit-btn" href="#" data-id="{{ $item->id }}">
                                            <i class="lni lni-pencil me-2"></i>Edit
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item text-danger delete-btn" href="#"
                                            data-id="{{ $item->id }}">
                                            <i class="lni lni-trash me-2"></i>Hapus
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Wajib Retribusi -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahModalLabel">Tambah Wajib Retribusi</h5>
                    <button type="button" class="btn-close Close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="WajibRetribusiForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="Npwrd" class="form-label">NPWRD <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="npwrd" name="npwrd"
                                placeholder="Nomor Pokok Wajib Retribusi Daerah">
                            <div class="invalid-feedback" id="errorNpwrd"></div>
                        </div>

                        <div class="mb-3">
                            <label for="Nama" class="form-label">Nama <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama" name="nama"
                                placeholder="Nama Wajib Retribusi">
                            <div class="invalid-feedback" id="errorNama"></div>
                        </div>

                        <div class="mb-3">
                            <label for="Nik" class="form-label">NIK <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nik" name="nik"
                                placeholder="Nomor Induk Kependudukan">
                            <div class="invalid-feedback" id="errorNik"></div>
                        </div>

                        <div class="mb-3">
                            <label for="Alamat" class="form-label">Alamat <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="alamat" name="alamat" rows="2" placeholder="Alamat lengkap"></textarea>
                            <div class="invalid-feedback" id="errorAlamat"></div>
                        </div>

                        <div class="mb-3">
                            <label for="NoTelp" class="form-label">No. Telepon</label>
                            <input type="text" class="form-control" id="noTelp" name="no_telp"
                                placeholder="Nomor Telepon">
                            <div class="invalid-feedback" id="errorNoTelp"></div>
                        </div>

                        <div class="mb-3">
                            <label for="Email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email"
                                placeholder="Email aktif">
                            <div class="invalid-feedback" id="errorEmail"></div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm Close" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i> Batal
                        </button>
                        <button type="submit" class="btn btn-success btn-sm">
                            <i class="lni lni-save"></i> Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <!-- Modal Edit Wajib Retribusi -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Edit Wajib Retribusi</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <form id="editWajibRetribusiForm">
                    <input type="hidden" id="editId" name="id">

                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editNpwrd" class="form-label">NPWRD <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editNpwrd" name="npwrd"
                                placeholder="Nomor Pokok Wajib Retribusi Daerah">
                            <div class="invalid-feedback" id="editErrorNpwrd"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editNama" class="form-label">Nama <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editNama" name="nama"
                                placeholder="Nama Wajib Retribusi">
                            <div class="invalid-feedback" id="editErrorNama"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editNik" class="form-label">NIK <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editNik" name="nik"
                                placeholder="Nomor Induk Kependudukan">
                            <div class="invalid-feedback" id="editErrorNik"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editAlamat" class="form-label">Alamat <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="editAlamat" name="alamat" rows="2" placeholder="Alamat lengkap"></textarea>
                            <div class="invalid-feedback" id="editErrorAlamat"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editNoTelp" class="form-label">No. Telepon</label>
                            <input type="text" class="form-control" id="editNoTelp" name="no_telp"
                                placeholder="Nomor Telepon">
                            <div class="invalid-feedback" id="editErrorNoTelp"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="editEmail" name="email"
                                placeholder="Email aktif">
                            <div class="invalid-feedback" id="editErrorEmail"></div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i> Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i> Simpan Perubahan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                $('.addClose, .editClose').on('click', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                $('#tambahModal, #editModal').on('hidden.bs.modal', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                $('#WajibRetribusiForm').submit(function(e) {
                    e.preventDefault();

                    let formData = {
                        npwrd: $('#npwrd').val(),
                        nama: $('#nama').val(),
                        nik: $('#nik').val(),
                        alamat: $('#alamat').val(),
                        no_telp: $('#noTelp').val(),
                        email: $('#email').val()
                    };

                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('retribusi.wajib.store') }}',
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#WajibRetribusiForm')[0].reset();
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, '');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });


                $('.edit-btn').on('click', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');

                    $.ajax({
                        url: '{{ route('retribusi.wajib.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editId').val(response.id);
                            $('#editNpwrd').val(response.npwrd);
                            $('#editNama').val(response.nama);
                            $('#editNik').val(response.nik);
                            $('#editAlamat').val(response.alamat);
                            $('#editNoTelp').val(response.no_telp);
                            $('#editEmail').val(response.email);

                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data wajib retribusi');
                        }

                    });
                });

                $('#editWajibRetribusiForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editId').val();
                    let formData = {
                        npwrd: $('#editNpwrd').val(),
                        nama: $('#editNama').val(),
                        nik: $('#editNik').val(),
                        alamat: $('#editAlamat').val(),
                        no_telp: $('#editNoTelp').val(),
                        email: $('#editEmail').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '{{ route('retribusi.wajib.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: formData,
                        success: function(response) {
                            $('#editWajibRetribusiModal').modal('hide');
                            round_success_noti(response.success || 'Data berhasil diperbarui!');
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'edit');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                function handleValidationErrors(errors, prefix) {
                    const fieldMap = {
                        'npwrd': prefix + 'Npwrd',
                        'nama': prefix + 'Nama',
                        'nik': prefix + 'Nik',
                        'alamat': prefix + 'Alamat',
                        'no_telp': prefix + 'NoTelp',
                        'email': prefix + 'Email'
                    };


                    Object.keys(errors).forEach(function(key) {
                        if (fieldMap[key]) {
                            let inputId = fieldMap[key].charAt(0).toLowerCase() + fieldMap[key].slice(1);
                            $('#' + inputId).addClass('is-invalid');

                            let errorId = prefix ? 'editError' + fieldMap[key].replace(prefix, '') : 'error' +
                                fieldMap[key];
                            $('#' + errorId).text(errors[key][0]);
                        }
                    });
                }

                function loadTableData() {
                    $.ajax({
                        url: '{{ route('retribusi.wajib.index') }}',
                        method: 'GET',
                        success: function(response) {
                            let newTableBody = $(response).find('tbody').html();
                            $('tbody').html(newTableBody);
                        },
                        error: function() {
                            console.log('Error loading table data');
                        }
                    });
                }

                $(document).on('click', '.delete-btn', function(e) {
                    e.preventDefault();
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus data wajib retribusi ini?')) {
                        $.ajax({
                            url: '{{ route('retribusi.wajib.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });


            });
        </script>
    @endpush
@endsection
