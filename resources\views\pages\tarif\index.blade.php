@extends('layouts.app')

@section('title', 'Data Tarif')

@section('content')
    <x-page-content>
        <x-page-breadcrumb title="Data Master">
            <x-breadcrumb-item>Tarif</x-breadcrumb-item>
        </x-page-breadcrumb>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal"><i
                    class="lni lni-plus"></i> Tambah</button>
        </div>
        <x-card>
            <x-data-table>
                <x-slot name="thead">
                    <tr>
                        <th>No</th>
                        <th>Tipe</th>
                        <th>Jenis</th>
                        <th>Tarif</th>
                        <th>Aksi</th>
                    </tr>
                </x-slot>
                @foreach ($tarifs as $tarif)
                    <tr id="row_{{ $tarif->id }}">
                        <td>{{ $loop->iteration }}</td>
                        <td>Tipe {{ $tarif->tipe }}</td>
                        <td>{{ $tarif->jenis }}</td>
                        <td>Rp {{ number_format($tarif->tarif, 0, ',', '.') }}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-warning edit-btn"
                                data-id="{{ $tarif->id }}">
                                <i class="lni lni-pencil"></i> Edit
                            </button>
                            <button type="button" class="btn btn-sm btn-danger deleteTarif"
                                data-id="{{ $tarif->id }}">
                                <i class="lni lni-trash"></i> Hapus
                            </button>
                        </td>
                    </tr>
                @endforeach
            </x-data-table>
        </x-card>
    </x-page-content>

    <!-- Modal Tambah Tarif -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahModalLabel">Tambah Tarif</h5>
                    <button type="button" class="btn-close addClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="tipeTarif" class="form-label">Pilih Tipe <span class="text-danger">*</span></label>
                            <select class="form-select" id="tipeTarif" name="tipe" >
                                <option value="">-- Pilih Tipe --</option>
                                <option value="A">Tipe A</option>
                                <option value="B">Tipe B</option>
                                <option value="C">Tipe C</option>
                                <option value="D">Tipe D</option>
                            </select>
                            <div class="invalid-feedback" id="errorTipeTarif"></div>
                        </div>
                        <div class="mb-3">
                            <label for="jenisTarif" class="form-label">Pilih Jenis <span
                                    class="text-danger">*</span></label>
                            <select class="form-select" id="jenisTarif" name="jenis" >
                                <option value="">-- Pilih Jenis --</option>
                                <option value="Kios">Kios</option>
                                <option value="Los">Los</option>
                                <option value="Selasar">Selasar</option>
                            </select>
                            <div class="invalid-feedback" id="errorJenisTarif"></div>
                        </div>

                        <div class="mb-3">
                            <label for="nominalTarif" class="form-label">Tarif <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="nominalTarif" name="tarif"
                                placeholder="Nominal Tarif" min="0" >
                            <div class="invalid-feedback" id="errorNominalTarif"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm addClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Tarif -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Edit Tarif</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <form id="editForm">
                    <input type="hidden" id="editTarifId" name="id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editTipeTarif" class="form-label">Pilih Tipe <span
                                    class="text-danger">*</span></label>
                            <select class="form-select" id="editTipeTarif" name="tipe" >
                                <option value="">-- Pilih Tipe --</option>
                                <option value="A">Tipe A</option>
                                <option value="B">Tipe B</option>
                                <option value="C">Tipe C</option>
                                <option value="D">Tipe D</option>
                            </select>
                            <div class="invalid-feedback" id="editErrorTipeTarif"></div>
                        </div>
                        <div class="mb-3">
                            <label for="editJenisTarif" class="form-label">Pilih Jenis <span
                                    class="text-danger">*</span></label>
                            <select class="form-select" id="editJenisTarif" name="jenis" >
                                <option value="">-- Pilih Jenis --</option>
                                <option value="Kios">Kios</option>
                                <option value="Los">Los</option>
                                <option value="Selasar">Selasar</option>
                            </select>
                            <div class="invalid-feedback" id="editErrorJenisTarif"></div>
                        </div>

                        <div class="mb-3">
                            <label for="editNominalTarif" class="form-label">Tarif <span
                                    class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="editNominalTarif" name="tarif"
                                placeholder="Nominal Tarif" min="0" >
                            <div class="invalid-feedback" id="editErrorNominalTarif"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });



                // Handle form tambah tarif
                $('#tambahForm').submit(function(e) {
                    e.preventDefault();

                    let tipeTarif = $('#tipeTarif').val();
                    let jenisTarif = $('#jenisTarif').val();
                    let nominalTarif = $('#nominalTarif').val();

                    // Reset validation
                    $('#tipeTarif').removeClass('is-invalid');
                    $('#jenisTarif').removeClass('is-invalid');
                    $('#nominalTarif').removeClass('is-invalid');
                    $('#errorTipeTarif').text('');
                    $('#errorJenisTarif').text('');
                    $('#errorNominalTarif').text('');

                    $.ajax({
                        url: '{{ route('tarif.store') }}',
                        method: 'POST',
                        data: {
                            tipe: tipeTarif,
                            jenis: jenisTarif,
                            tarif: nominalTarif
                        },
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahForm')[0].reset();
                            round_success_noti(response.success);

                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;

                                if (errors.tipe) {
                                    $('#tipeTarif').addClass('is-invalid');
                                    $('#errorTipeTarif').text(errors.tipe[0]);
                                }
                                if (errors.jenis) {
                                    $('#jenisTarif').addClass('is-invalid');
                                    $('#errorJenisTarif').text(errors.jenis[0]);
                                }
                                if (errors.tarif) {
                                    $('#nominalTarif').addClass('is-invalid');
                                    $('#errorNominalTarif').text(errors.tarif[0]);
                                }
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                $(document).on('click', '.edit-btn', function() {
                    let id = $(this).data('id');
                    $.ajax({
                        url: '{{ route('tarif.edit', ':id') }}'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editTarifId').val(response.id);
                            $('#editTipeTarif').val(response.tipe);
                            $('#editJenisTarif').val(response.jenis);
                            $('#editNominalTarif').val(response.tarif);

                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data tarif');
                        }
                    });
                });

                // Handle form edit tarif
                $('#editForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editTarifId').val();
                    let tipeTarif = $('#editTipeTarif').val();
                    let jenisTarif = $('#editJenisTarif').val();
                    let nominalTarif = $('#editNominalTarif').val();

                    // Reset validation
                    $('#editTipeTarif').removeClass('is-invalid');
                    $('#editJenisTarif').removeClass('is-invalid');
                    $('#editNominalTarif').removeClass('is-invalid');
                    $('#editErrorTipeTarif').text('');
                    $('#editErrorJenisTarif').text('');
                    $('#editErrorNominalTarif').text('');

                    $.ajax({
                        url: '{{ route('tarif.update', ':id') }}'.replace(':id', id),
                        method: 'PUT',
                        data: {
                            tipe: tipeTarif,
                            jenis: jenisTarif,
                            tarif: nominalTarif
                        },
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;

                                if (errors.tipe) {
                                    $('#editTipeTarif').addClass('is-invalid');
                                    $('#editErrorTipeTarif').text(errors.tipe[0]);
                                }
                                if (errors.jenis) {
                                    $('#editJenisTarif').addClass('is-invalid');
                                    $('#editErrorJenisTarif').text(errors.jenis[0]);
                                }
                                if (errors.tarif) {
                                    $('#editNominalTarif').addClass('is-invalid');
                                    $('#editErrorNominalTarif').text(errors.tarif[0]);
                                }
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle delete button click
                $(document).on('click', '.deleteTarif', function() {
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus data tarif ini?')) {
                        $.ajax({
                            url: '{{ route('tarif.destroy', ':id') }}'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                // Handle modal close events
                $('.addClose, .editClose').on('click', function() {
                    clearValidation('#tambahForm');
                    clearValidation('#editForm');
                });

                $('#tambahModal').on('hidden.bs.modal', function() {
                    clearValidation('#tambahForm');
                });

                $('#editModal').on('hidden.bs.modal', function() {
                    clearValidation('#editForm');
                });

                function clearValidation(formSelector) {
                    $(formSelector + ' .form-control, ' + formSelector + ' .form-select').removeClass('is-invalid');
                    $(formSelector + ' .invalid-feedback').text('');
                }

                function loadTableData() {
                    $.ajax({
                        url: '{{ route('tarif.index') }}',
                        method: 'GET',
                        success: function(response) {
                            let newTableBody = $(response).find('tbody').html();
                            $('tbody').html(newTableBody);
                        },
                        error: function() {
                            console.log('Error loading table data');
                        }
                    });
                }
            });
        </script>
    @endpush
