<?php

use App\Models\ObjekRetribusi;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LosController;
use App\Http\Controllers\KiosController;
use App\Http\Controllers\PasarController;
use App\Http\Controllers\TarifController;
use App\Http\Controllers\DaganganController;
use App\Http\Controllers\KepalaDinasController;
use App\Http\Controllers\KepalaPasarController;
use App\Http\Controllers\ObjekRetribusiController;
use App\Http\Controllers\PermohonanBaruController;
use App\Http\Controllers\WajibRetribusiController;
use App\Http\Controllers\PermohonanPerpanjangController;
use App\Http\Controllers\DownloadDokumenPermohonanController;

Route::get('/', function () {
    return view('welcome');
});

Route::prefix('/data-master')->group(function () {
    Route::prefix('/pasar')->group(function () {
        Route::get('/', [PasarController::class, 'index'])->name('pasar.index');
        Route::post('/store', [PasarController::class, 'store'])->name('pasar.store');
        Route::get('/{pasar}/edit', [PasarController::class, 'edit'])->name('pasar.edit');
        Route::put('/{pasar}', [PasarController::class, 'update'])->name('pasar.update');
        Route::delete('/{pasar}', [PasarController::class, 'destroy'])->name('pasar.destroy');
    });

    Route::prefix('/dagangan')->group(function () {
        Route::get('/', [DaganganController::class, 'index'])->name('dagangan.index');
        Route::post('/store', [DaganganController::class, 'store'])->name('dagangan.store');
        Route::get('/{dagangan}/edit', [DaganganController::class, 'edit'])->name('dagangan.edit');
        Route::put('/{dagangan}', [DaganganController::class, 'update'])->name('dagangan.update');
        Route::delete('/{dagangan}', [DaganganController::class, 'destroy'])->name('dagangan.destroy');
    });

    Route::prefix('/tarif')->group(function () {
        Route::get('/', [TarifController::class, 'index'])->name('tarif.index');
        Route::post('/store', [TarifController::class, 'store'])->name('tarif.store');
        Route::get('/{tarif}/edit', [TarifController::class, 'edit'])->name('tarif.edit');
        Route::put('/{tarif}', [TarifController::class, 'update'])->name('tarif.update');
        Route::delete('/{tarif}', [TarifController::class, 'destroy'])->name('tarif.destroy');
    });

    Route::prefix('/kios')->group(function () {
        Route::get('/', [KiosController::class, 'index'])->name('kios.index');
        Route::post('/store', [KiosController::class, 'store'])->name('kios.store');
        Route::get('/{kios}/edit', [KiosController::class, 'edit'])->name('kios.edit');
        Route::put('/{kios}', [KiosController::class, 'update'])->name('kios.update');
        Route::delete('/{kios}', [KiosController::class, 'destroy'])->name('kios.destroy');
        Route::post('/get-tarif-by-pasar', [KiosController::class, 'getTarifByPasar'])->name('kios.getTarifByPasar');
    });

    Route::prefix('/los')->group(function () {
        Route::get('/', [LosController::class, 'index'])->name('los.index');
        Route::post('/store', [LosController::class, 'store'])->name('los.store');
        Route::get('/{los}/edit', [LosController::class, 'edit'])->name('los.edit');
        Route::put('/{los}', [LosController::class, 'update'])->name('los.update');
        Route::delete('/{los}', [LosController::class, 'destroy'])->name('los.destroy');
        Route::post('/get-tarif-by-pasar', [LosController::class, 'getTarifByPasar'])->name('los.getTarifByPasar');
    });

    Route::prefix('/kepala-pasar')->group(function () {
        Route::get('/', [KepalaPasarController::class, 'index'])->name('kepala-pasar.index');
        Route::post('/store', [KepalaPasarController::class, 'store'])->name('kepala-pasar.store');
        Route::get('/{kepalaPasar}/edit', [KepalaPasarController::class, 'edit'])->name('kepala-pasar.edit');
        Route::put('/{kepalaPasar}', [KepalaPasarController::class, 'update'])->name('kepala-pasar.update');
        Route::delete('/{kepalaPasar}', [KepalaPasarController::class, 'destroy'])->name('kepala-pasar.destroy');
    });

    Route::prefix('/kepala-dinas')->group(function () {
        Route::get('/', [KepalaDinasController::class, 'index'])->name('kepala-dinas.index');
        Route::post('/store', [KepalaDinasController::class, 'store'])->name('kepala-dinas.store');
        Route::get('/{kepalaDinas}/edit', [KepalaDinasController::class, 'edit'])->name('kepala-dinas.edit');
        Route::put('/{kepalaDinas}', [KepalaDinasController::class, 'update'])->name('kepala-dinas.update');
        Route::delete('/{kepalaDinas}', [KepalaDinasController::class, 'destroy'])->name('kepala-dinas.destroy');
    });
});

Route::prefix('/permohonan')->group(function () {
    Route::get('/baru', [PermohonanBaruController::class, 'index'])->name('permohonan.baru.index');
    Route::post('/baru/store', [PermohonanBaruController::class, 'store'])->name('permohonan.baru.store');
    Route::get('/baru/{permohonanBaru}/edit', [PermohonanBaruController::class, 'edit'])->name('permohonan.baru.edit');
    Route::put('/baru/{permohonanBaru}', [PermohonanBaruController::class, 'update'])->name('permohonan.baru.update');
    Route::delete('/baru/{permohonanBaru}', [PermohonanBaruController::class, 'destroy'])->name('permohonan.baru.destroy');
    Route::post('/baru/{permohonanBaru}/upload', [PermohonanBaruController::class, 'uploadDokumen'])->name('permohonan.baru.upload');
    Route::get('/baru/{id}/{jenis}/download', [DownloadDokumenPermohonanController::class, 'baru'])->name('permohonan.baru.downlaod.dokumen');
    Route::put('/baru/{permohonanBaru}/verifikasi', [PermohonanBaruController::class, 'verifikasi'])
        ->name('permohonan.baru.verifikasi');


    Route::get('/perpanjang', [PermohonanPerpanjangController::class, 'index'])->name('permohonan.perpanjang.index');


    Route::post('/perpanjang/get-lapak', [PermohonanPerpanjangController::class, 'getLapak'])->name('permohonan.perpanjang.getLapak');
});

Route::prefix('/retribusi')->group(function () {
    Route::prefix('/wajib-retribusi')->group(function () {
        Route::get('/', [WajibRetribusiController::class, 'index'])->name('retribusi.wajib.index');
        Route::post('/store', [WajibRetribusiController::class, 'store'])->name('retribusi.wajib.store');
        Route::get('/{wajibRetribusi}/edit', [WajibRetribusiController::class, 'edit'])->name('retribusi.wajib.edit');
        Route::put('/{wajibRetribusi}', [WajibRetribusiController::class, 'update'])->name('retribusi.wajib.update');
        Route::delete('/{wajibRetribusi}', [WajibRetribusiController::class, 'destroy'])->name('retribusi.wajib.destroy');
    });
    Route::prefix('/objek-retribusi')->group(function () {
        Route::get('/', [ObjekRetribusiController::class, 'index'])->name('retribusi.objek.index');
        Route::post('/store', [ObjekRetribusiController::class, 'store'])->name('retribusi.objek.store');
        Route::get('/{objekRetribusi}/edit', [ObjekRetribusiController::class, 'edit'])->name('retribusi.objek.edit');
        Route::put('/{objekRetribusi}', [ObjekRetribusiController::class, 'update'])->name('retribusi.objek.update');
        Route::delete('/{objekRetribusi}', [ObjekRetribusiController::class, 'destroy'])->name('retribusi.objek.destroy');
    });
});
