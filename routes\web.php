<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PasarController;
use App\Http\Controllers\DaganganController;
use App\Http\Controllers\TarifController;
use App\Http\Controllers\KiosController;
use App\Http\Controllers\LosController;

Route::get('/', function () {
    return view('welcome');
});

Route::prefix('/data-master')->group(function () {
    Route::prefix('/pasar')->group(function () {
        Route::get('/', [PasarController::class, 'index'])->name('pasar.index');
        Route::post('/store', [PasarController::class, 'store'])->name('pasar.store');
        Route::get('/{pasar}/edit', [PasarController::class, 'edit'])->name('pasar.edit');
        Route::put('/{pasar}', [PasarController::class, 'update'])->name('pasar.update');
        Route::delete('/{pasar}', [PasarController::class, 'destroy'])->name('pasar.destroy');
    });

    Route::prefix('/dagangan')->group(function () {
        Route::get('/', [DaganganController::class, 'index'])->name('dagangan.index');
        Route::post('/store', [DaganganController::class, 'store'])->name('dagangan.store');
        Route::get('/{dagangan}/edit', [DaganganController::class, 'edit'])->name('dagangan.edit');
        Route::put('/{dagangan}', [DaganganController::class, 'update'])->name('dagangan.update');
        Route::delete('/{dagangan}', [DaganganController::class, 'destroy'])->name('dagangan.destroy');
    });

    Route::prefix('/tarif')->group(function () {
        Route::get('/', [TarifController::class, 'index'])->name('tarif.index');
        Route::post('/store', [TarifController::class, 'store'])->name('tarif.store');
        Route::get('/{tarif}/edit', [TarifController::class, 'edit'])->name('tarif.edit');
        Route::put('/{tarif}', [TarifController::class, 'update'])->name('tarif.update');
        Route::delete('/{tarif}', [TarifController::class, 'destroy'])->name('tarif.destroy');
    });

    Route::prefix('/kios')->group(function () {
        Route::get('/', [KiosController::class, 'index'])->name('kios.index');
        Route::post('/store', [KiosController::class, 'store'])->name('kios.store');
        Route::get('/{kios}/edit', [KiosController::class, 'edit'])->name('kios.edit');
        Route::put('/{kios}', [KiosController::class, 'update'])->name('kios.update');
        Route::delete('/{kios}', [KiosController::class, 'destroy'])->name('kios.destroy');
    });

    Route::prefix('/los')->group(function () {
        Route::get('/', [LosController::class, 'index'])->name('los.index');
        Route::post('/store', [LosController::class, 'store'])->name('los.store');
        Route::get('/{los}/edit', [LosController::class, 'edit'])->name('los.edit');
        Route::put('/{los}', [LosController::class, 'update'])->name('los.update');
        Route::delete('/{los}', [LosController::class, 'destroy'])->name('los.destroy');
    });
});
