<?php $__env->startSection('title', 'Permohonan Baru'); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .filepond--credits {
            display: none !important;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <?php if (isset($component)) { $__componentOriginal562cb1477a8769da678d472fe5deeba8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal562cb1477a8769da678d472fe5deeba8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-content','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-content'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
        <?php if (isset($component)) { $__componentOriginalcc4606514148ae556824e7690929079d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc4606514148ae556824e7690929079d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-breadcrumb','data' => ['title' => 'Permohonan']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Permohonan']); ?>
            <?php if (isset($component)) { $__componentOriginal0a52b4bd723bff2309fa8ff7e970e261 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0a52b4bd723bff2309fa8ff7e970e261 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb-item','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>Permohonan Baru <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0a52b4bd723bff2309fa8ff7e970e261)): ?>
<?php $attributes = $__attributesOriginal0a52b4bd723bff2309fa8ff7e970e261; ?>
<?php unset($__attributesOriginal0a52b4bd723bff2309fa8ff7e970e261); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0a52b4bd723bff2309fa8ff7e970e261)): ?>
<?php $component = $__componentOriginal0a52b4bd723bff2309fa8ff7e970e261; ?>
<?php unset($__componentOriginal0a52b4bd723bff2309fa8ff7e970e261); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc4606514148ae556824e7690929079d)): ?>
<?php $attributes = $__attributesOriginalcc4606514148ae556824e7690929079d; ?>
<?php unset($__attributesOriginalcc4606514148ae556824e7690929079d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc4606514148ae556824e7690929079d)): ?>
<?php $component = $__componentOriginalcc4606514148ae556824e7690929079d; ?>
<?php unset($__componentOriginalcc4606514148ae556824e7690929079d); ?>
<?php endif; ?>
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#tambahModal">
                <i class="lni lni-plus"></i> Tambah Permohonan
            </button>
        </div>
        <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
            <?php if (isset($component)) { $__componentOriginalc8463834ba515134d5c98b88e1a9dc03 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc8463834ba515134d5c98b88e1a9dc03 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.data-table','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('data-table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                 <?php $__env->slot('thead', null, []); ?> 
                    <tr>
                        <th>No</th>
                        <th>Nama Pemohon</th>
                        <th>NPWRD</th>
                        <th>NIK</th>
                        <th>Lapak</th>
                        <th>Jenis Dagangan</th>
                        <th>Status</th>
                        <th>Surat</th>
                        <th>Aksi</th>
                    </tr>
                 <?php $__env->endSlot(); ?>
                <?php $__currentLoopData = $permohonans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr id="row_<?php echo e($item->id); ?>">
                        <td><?php echo e($loop->iteration); ?></td>
                        <td><?php echo e($item->nama); ?></td>
                        <td><?php echo e($item->npwrd); ?></td>
                        <td><?php echo e($item->nik); ?></td>
                        <td>
                            <?php echo e($item->lapak->nomor ?? '-'); ?> - <?php echo e($item->lapak->nama_blok ?? '-'); ?>

                            <br><small class="text-muted"><?php echo e($item->lapak->pasar->nama_pasar ?? '-'); ?></small>
                        </td>
                        <td><?php echo e($item->dagangan->nama_dagangan ?? '-'); ?></td>
                        <td>
                            <?php if($item->status == 'proses'): ?>
                                <span class="badge bg-warning">Proses</span>
                            <?php elseif($item->status == 'menunggu_verifikasi'): ?>
                                <span class="badge bg-info">Menunggu Verifikasi</span>
                            <?php elseif($item->status == 'diterima'): ?>
                                <span class="badge bg-success">Diterima</span>
                            <?php elseif($item->status == 'ditolak'): ?>
                                <span class="badge bg-danger">Ditolak</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group-vertical" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="<?php echo e($item->id); ?>" data-jenis="sp_kepala">
                                    <i class="lni lni-download"></i> SP Kepala
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="<?php echo e($item->id); ?>" data-jenis="ba_penunjukan">
                                    <i class="lni lni-download"></i> BA Penunjukan
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="<?php echo e($item->id); ?>" data-jenis="sp_pemilik">
                                    <i class="lni lni-download"></i> SP Pemilik
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm download-btn"
                                    data-id="<?php echo e($item->id); ?>" data-jenis="surat_pernyataan">
                                    <i class="lni lni-download"></i> Surat Pernyataan
                                </button>
                            </div>
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-warning edit-btn" data-id="<?php echo e($item->id); ?>">
                                <i class="lni lni-pencil"></i> Edit
                            </button>
                            <button type="button" class="btn btn-sm btn-info upload-btn" data-id="<?php echo e($item->id); ?>">
                                <i class="lni lni-upload"></i> Upload
                            </button>
                            <button type="button" class="btn btn-sm btn-danger deletePermohonan"
                                data-id="<?php echo e($item->id); ?>">
                                <i class="lni lni-trash"></i> Hapus
                            </button>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc8463834ba515134d5c98b88e1a9dc03)): ?>
<?php $attributes = $__attributesOriginalc8463834ba515134d5c98b88e1a9dc03; ?>
<?php unset($__attributesOriginalc8463834ba515134d5c98b88e1a9dc03); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc8463834ba515134d5c98b88e1a9dc03)): ?>
<?php $component = $__componentOriginalc8463834ba515134d5c98b88e1a9dc03; ?>
<?php unset($__componentOriginalc8463834ba515134d5c98b88e1a9dc03); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal562cb1477a8769da678d472fe5deeba8)): ?>
<?php $attributes = $__attributesOriginal562cb1477a8769da678d472fe5deeba8; ?>
<?php unset($__attributesOriginal562cb1477a8769da678d472fe5deeba8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal562cb1477a8769da678d472fe5deeba8)): ?>
<?php $component = $__componentOriginal562cb1477a8769da678d472fe5deeba8; ?>
<?php unset($__componentOriginal562cb1477a8769da678d472fe5deeba8); ?>
<?php endif; ?>

    <!-- Modal Tambah Permohonan -->
    <div class="modal fade" id="tambahModal" tabindex="-1" aria-labelledby="tambahModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahModalLabel">Tambah Permohonan Baru</h5>
                    <button type="button" class="btn-close addClose" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="tambahForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="lapakPermohonan" class="form-label">Pilih Lapak <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="lapakPermohonan" name="lapak_id" required>
                                        <option value="">-- Pilih Lapak --</option>
                                        <?php $__currentLoopData = $lapaks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lapak): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($lapak->id); ?>">
                                                <?php echo e($lapak->nomor); ?> - <?php echo e($lapak->nama_blok); ?>

                                                (<?php echo e($lapak->pasar->nama_pasar); ?>)
                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <div class="invalid-feedback" id="errorLapakPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="daganganPermohonan" class="form-label">Jenis Dagangan <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="daganganPermohonan" name="dagangan_id" required>
                                        <option value="">-- Pilih Jenis Dagangan --</option>
                                        <?php $__currentLoopData = $dagangans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dagangan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($dagangan->id); ?>"><?php echo e($dagangan->nama_dagangan); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <div class="invalid-feedback" id="errorDaganganPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="namaPermohonan" class="form-label">Nama Lengkap <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="namaPermohonan" name="nama"
                                        placeholder="Nama Lengkap" required>
                                    <div class="invalid-feedback" id="errorNamaPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nikPermohonan" class="form-label">NIK <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nikPermohonan" name="nik"
                                        placeholder="NIK" maxlength="16" required>
                                    <div class="invalid-feedback" id="errorNikPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="alamatPermohonan" class="form-label">Alamat <span
                                            class="text-danger">*</span></label>
                                    <textarea class="form-control" id="alamatPermohonan" name="alamat" rows="3" placeholder="Alamat Lengkap"
                                        required></textarea>
                                    <div class="invalid-feedback" id="errorAlamatPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="noTlpPermohonan" class="form-label">No. Telepon</label>
                                    <input type="text" class="form-control" id="noTlpPermohonan" name="no_tlp"
                                        placeholder="No. Telepon">
                                    <div class="invalid-feedback" id="errorNoTlpPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="emailPermohonan" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="emailPermohonan" name="email"
                                        placeholder="Email">
                                    <div class="invalid-feedback" id="errorEmailPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="npwrdPermohonan" class="form-label">NPWRD</label>
                                    <input type="text" class="form-control" id="npwrdPermohonan" name="npwrd"
                                        placeholder="NPWRD">
                                    <div class="invalid-feedback" id="errorNpwrdPermohonan"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm addClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Permohonan -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Edit Permohonan Baru</h5>
                    <button type="button" class="btn-close editClose" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <form id="editForm">
                    <input type="hidden" id="editPermohonanId" name="id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editLapakPermohonan" class="form-label">Pilih Lapak <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editLapakPermohonan" name="lapak_id" required>
                                        <option value="">-- Pilih Lapak --</option>
                                        <?php $__currentLoopData = $lapaks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lapak): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($lapak->id); ?>">
                                                <?php echo e($lapak->nomor); ?> - <?php echo e($lapak->nama_blok); ?>

                                                (<?php echo e($lapak->pasar->nama_pasar); ?>)
                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <div class="invalid-feedback" id="editErrorLapakPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editDaganganPermohonan" class="form-label">Jenis Dagangan <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editDaganganPermohonan" name="dagangan_id" required>
                                        <option value="">-- Pilih Jenis Dagangan --</option>
                                        <?php $__currentLoopData = $dagangans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dagangan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($dagangan->id); ?>"><?php echo e($dagangan->nama_dagangan); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <div class="invalid-feedback" id="editErrorDaganganPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNamaPermohonan" class="form-label">Nama Lengkap <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editNamaPermohonan" name="nama"
                                        placeholder="Nama Lengkap" required>
                                    <div class="invalid-feedback" id="editErrorNamaPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNikPermohonan" class="form-label">NIK <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editNikPermohonan" name="nik"
                                        placeholder="NIK" maxlength="16" required>
                                    <div class="invalid-feedback" id="editErrorNikPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editNoTlpPermohonan" class="form-label">No. Telepon</label>
                                    <input type="text" class="form-control" id="editNoTlpPermohonan" name="no_tlp"
                                        placeholder="No. Telepon">
                                    <div class="invalid-feedback" id="editErrorNoTlpPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editEmailPermohonan" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="editEmailPermohonan" name="email"
                                        placeholder="Email">
                                    <div class="invalid-feedback" id="editErrorEmailPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editNpwrdPermohonan" class="form-label">NPWRD</label>
                                    <input type="text" class="form-control" id="editNpwrdPermohonan" name="npwrd"
                                        placeholder="NPWRD">
                                    <div class="invalid-feedback" id="editErrorNpwrdPermohonan"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="editStatusPermohonan" class="form-label">Status <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="editStatusPermohonan" name="status" required>
                                        <option value="">-- Pilih Status --</option>
                                        <option value="proses">Proses</option>
                                        <option value="menunggu_verifikasi">Menunggu Verifikasi</option>
                                        <option value="diterima">Diterima</option>
                                        <option value="ditolak">Ditolak</option>
                                    </select>
                                    <div class="invalid-feedback" id="editErrorStatusPermohonan"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="editKeteranganPermohonan" class="form-label">Keterangan</label>
                                    <textarea class="form-control" id="editKeteranganPermohonan" name="keterangan" rows="2"
                                        placeholder="Keterangan (opsional)"></textarea>
                                    <div class="invalid-feedback" id="editErrorKeteranganPermohonan"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm editClose" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i>
                            Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-save"></i>
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Upload Surat -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" style="display: none;"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">Upload Surat</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="uploadForm" enctype="multipart/form-data">
                    <input type="hidden" id="uploadPermohonanId" name="permohonan_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <h6 class="mb-0">Data Permohonan</h6>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Nama:</strong> <span id="uploadNama">-</span></p>
                                        <p><strong>NIK:</strong> <span id="uploadNik">-</span></p>
                                        <p><strong>NPWRD:</strong> <span id="uploadNPWRD">-</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Lapak:</strong> <span id="uploadLapak">-</span></p>
                                        <p><strong>Jenis Dagangan:</strong> <span id="uploadDagangan">-</span></p>
                                        <p><strong>Alamat:</strong> <span id="uploadAlamat">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h6 class="mb-3">Dokumen Permohonan</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="ba_penunjukan" class="form-label">BA Penunjukan</label>
                                <input type="file" class="filepond" id="ba_penunjukan" name="ba_penunjukan"
                                    accept="image/*" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="sp_pemilik" class="form-label">SP Pemilik</label>
                                <input type="file" class="filepond" id="sp_pemilik" name="sp_pemilik"
                                    accept="image/*" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="surat_pernyataan" class="form-label">Surat Pernyataan</label>
                                <input type="file" class="filepond" id="surat_pernyataan" name="surat_pernyataan"
                                    accept="image/*" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="ktp" class="form-label">KTP</label>
                                <input type="file" class="filepond" id="ktp" name="ktp"
                                    accept="image/*" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="pas_foto" class="form-label">Pas Foto</label>
                                <input type="file" class="filepond" id="pas_foto" name="pas_foto"
                                    accept="image/*" />
                            </div>

                            <div class="mb-3">
                                <label class="form-label d-block mb-1" for="keterangan">Keterangan</label>
                                <textarea class="form-control" id="uploadKeterangan" name="keterangan" rows="2"
                                    placeholder="Keterangan (opsional)"></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label d-block mb-1" for="disetujui_kepala_pasar">Disetujui Kepala
                                    Pasar</label>
                                <div class="form-control d-flex align-items-center" style="height: 48px;">
                                    <input type="checkbox" class="form-check-input me-2" id="disetujui_kepala_pasar"
                                        name="disetujui_kepala_pasar" value="1">
                                    <span class="text-muted">Centang jika disetujui</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                            <i class="lni lni-close"></i> Tutup
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="lni lni-upload"></i> Upload
                        </button>
                    </div>
                </form>

            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // Initialize Select2
                $('#lapakPermohonan, #daganganPermohonan').select2({
                    dropdownParent: $('#tambahModal'),
                    theme: 'bootstrap4'
                });

                $('#editLapakPermohonan, #editDaganganPermohonan').select2({
                    dropdownParent: $('#editModal'),
                    theme: 'bootstrap4'
                });

                // Handle form tambah permohonan
                $('#tambahForm').submit(function(e) {
                    e.preventDefault();

                    let formData = {
                        lapak_id: $('#lapakPermohonan').val(),
                        dagangan_id: $('#daganganPermohonan').val(),
                        nama: $('#namaPermohonan').val(),
                        nik: $('#nikPermohonan').val(),
                        alamat: $('#alamatPermohonan').val(),
                        no_tlp: $('#noTlpPermohonan').val(),
                        email: $('#emailPermohonan').val(),
                        npwrd: $('#npwrdPermohonan').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '<?php echo e(route('permohonan.baru.store')); ?>',
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            $('#tambahModal').modal('hide');
                            $('#tambahForm')[0].reset();
                            $('#lapakPermohonan, #daganganPermohonan').val(null).trigger('change');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, '');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle edit button click
                $(document).on('click', '.edit-btn', function() {
                    let id = $(this).data('id');
                    $.ajax({
                        url: '<?php echo e(route('permohonan.baru.edit', ':id')); ?>'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#editPermohonanId').val(response.id);
                            $('#editLapakPermohonan').val(response.lapak_id).trigger('change');
                            $('#editDaganganPermohonan').val(response.dagangan_id).trigger(
                                'change');
                            $('#editNamaPermohonan').val(response.nama);
                            $('#editNikPermohonan').val(response.nik);
                            $('#editAlamatPermohonan').val(response.alamat);
                            $('#editNoTlpPermohonan').val(response.no_tlp);
                            $('#editEmailPermohonan').val(response.email);
                            $('#editNpwrdPermohonan').val(response.npwrd);
                            $('#editStatusPermohonan').val(response.status);
                            $('#editKeteranganPermohonan').val(response.keterangan);

                            $('#editModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data permohonan');
                        }
                    });
                });

                // Handle form edit permohonan
                $('#editForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#editPermohonanId').val();
                    let formData = {
                        lapak_id: $('#editLapakPermohonan').val(),
                        dagangan_id: $('#editDaganganPermohonan').val(),
                        nama: $('#editNamaPermohonan').val(),
                        nik: $('#editNikPermohonan').val(),
                        alamat: $('#editAlamatPermohonan').val(),
                        no_tlp: $('#editNoTlpPermohonan').val(),
                        email: $('#editEmailPermohonan').val(),
                        npwrd: $('#editNpwrdPermohonan').val(),
                        status: $('#editStatusPermohonan').val(),
                        keterangan: $('#editKeteranganPermohonan').val()
                    };

                    // Reset validation
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '<?php echo e(route('permohonan.baru.update', ':id')); ?>'.replace(':id', id),
                        method: 'PUT',
                        data: formData,
                        success: function(response) {
                            $('#editModal').modal('hide');
                            round_success_noti(response.success);
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'edit');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });

                // Handle delete button click
                $(document).on('click', '.deletePermohonan', function() {
                    let id = $(this).data('id');

                    if (confirm('Apakah Anda yakin ingin menghapus permohonan ini?')) {
                        $.ajax({
                            url: '<?php echo e(route('permohonan.baru.destroy', ':id')); ?>'.replace(':id', id),
                            method: 'DELETE',
                            success: function(response) {
                                round_success_noti(response.success);
                                loadTableData();
                            },
                            error: function(response) {
                                alert('Gagal menghapus data. Silakan coba lagi.');
                            }
                        });
                    }
                });

                // Handle download button click
                $(document).on('click', '.download-btn', function() {
                    let id = $(this).data('id');
                    let jenis = $(this).data('jenis');

                    // Untuk sementara alert, nanti bisa diganti dengan download sebenarnya
                    alert('Download ' + jenis + ' untuk ID: ' + id);

                    // Uncomment untuk implementasi download sebenarnya
                });


                // Handle validation errors
                function handleValidationErrors(errors, prefix) {
                    const fieldMap = {
                        'lapak_id': prefix + 'LapakPermohonan',
                        'dagangan_id': prefix + 'DaganganPermohonan',
                        'nama': prefix + 'NamaPermohonan',
                        'nik': prefix + 'NikPermohonan',
                        'alamat': prefix + 'AlamatPermohonan',
                        'no_tlp': prefix + 'NoTlpPermohonan',
                        'email': prefix + 'EmailPermohonan',
                        'npwrd': prefix + 'NpwrdPermohonan',
                        'status': prefix + 'StatusPermohonan',
                        'keterangan': prefix + 'KeteranganPermohonan'
                    };

                    Object.keys(errors).forEach(function(key) {
                        if (fieldMap[key]) {
                            $('#' + fieldMap[key]).addClass('is-invalid');
                            let errorId = prefix ? 'editError' + fieldMap[key].replace(prefix, '') : 'error' +
                                fieldMap[key];
                            $('#' + errorId).text(errors[key][0]);
                        }
                    });
                }

                // Handle modal close events
                $('.addClose, .editClose').on('click', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                $('#tambahModal, #editModal, #uploadModal').on('hidden.bs.modal', function() {
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                });

                function loadTableData() {
                    location.reload();
                }

                $(document).on('click', '.upload-btn', function() {
                    let id = $(this).data('id');
                    $.ajax({
                        url: '<?php echo e(route('permohonan.baru.edit', ':id')); ?>'.replace(':id', id),
                        method: 'GET',
                        success: function(response) {
                            $('#uploadPermohonanId').val(response.id);
                            $('#uploadNama').text(response.nama);
                            $('#uploadNik').text(response.nik);
                            $('#uploadAlamat').text(response.alamat);
                            $('#uploadLapak').text(response.lapak.jenis + ' - ' + response.lapak
                                .nomor + ' - ' + response.lapak.nama_blok + ' - ' + response
                                .lapak.pasar.nama_pasar);
                            $('#uploadDagangan').text(response.dagangan.nama_dagangan);
                            $('#uploadNPWRD').text(response.npwrd);
                            $('#uploadKeterangan').val(response.keterangan);

                            $('#uploadModal').modal('show');
                        },
                        error: function() {
                            alert('Gagal mengambil data permohonan');
                        }
                    });
                });

                // Handle form upload surat
                $('#uploadForm').submit(function(e) {
                    e.preventDefault();

                    let id = $('#uploadPermohonanId').val();
                    let formData = new FormData();
                    let hasFiles = false;

                    // Get files from FilePond instances manually
                    const filePondInputs = [
                        { id: 'fileBaPenunjukan', name: 'bapenunjukan' },
                        { id: 'fileSpPemilik', name: 'sppemilik' },
                        { id: 'fileSuratPernyataan', name: 'suratpernyataan' },
                        { id: 'fileKtp', name: 'ktp' },
                        { id: 'filePasFoto', name: 'pasfoto' }
                    ];

                    filePondInputs.forEach(function(input) {
                        const element = document.getElementById(input.id);
                        if (element && element.filePond) {
                            const files = element.filePond.getFiles();
                            console.log('FilePond files for ' + input.id + ':', files);

                            if (files.length > 0) {
                                // FilePond file object has .file property for actual File
                                const actualFile = files[0].file;
                                console.log('Actual file for ' + input.name + ':', actualFile);

                                if (actualFile instanceof File) {
                                    formData.append(input.name, actualFile);
                                    hasFiles = true;
                                    console.log('Added file to FormData:', input.name);
                                }
                            }
                        } else {
                            console.log('FilePond not found for:', input.id);
                        }
                    });

                    // Handle checkbox persetujuan kepala pasar
                    if ($('#accKepalaPasar').is(':checked')) {
                        formData.append('persetujuan_kepala_pasar', '1');
                        hasFiles = true; // Consider checkbox as "has data"
                    } else {
                        formData.append('persetujuan_kepala_pasar', '0');
                    }

                    // Add CSRF token
                    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

                    // Check if there's any data to upload
                    if (!hasFiles && !$('#accKepalaPasar').is(':checked')) {
                        alert('Pilih minimal satu file untuk diupload atau centang persetujuan kepala pasar');
                        return;
                    }

                    // Debug: log FormData contents
                    console.log('FormData contents:');
                    for (let pair of formData.entries()) {
                        console.log(pair[0] + ': ' + pair[1]);
                    }

                    // Reset error
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    $.ajax({
                        url: '<?php echo e(route('permohonan.baru.upload', ':id')); ?>'.replace(':id', id),
                        method: 'PUT',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#uploadModal').modal('hide');
                            round_success_noti(response.success || 'Dokumen berhasil diupload');
                            loadTableData();
                        },
                        error: function(response) {
                            if (response.status === 422) {
                                const errors = response.responseJSON.errors;
                                handleValidationErrors(errors, 'upload');
                            } else {
                                alert('Terjadi kesalahan. Silakan coba lagi.');
                            }
                        }
                    });
                });
            });
        </script>
        <script>
            FilePond.registerPlugin(
                FilePondPluginImagePreview,
                FilePondPluginFileValidateSize,
                FilePondPluginFileValidateType
            );
            initializeFilePond();

            function initializeFilePond() {
                const filePondElements = document.querySelectorAll('.filepond');

                filePondElements.forEach(function(element) {
                    const pond = FilePond.create(element, {
                        server: null,
                        allowMultiple: false,
                        maxFiles: 1,
                        maxFileSize: '2MB',
                        acceptedFileTypes: ['image/*', 'application/pdf'],
                        labelIdle: 'Drag & Drop file atau <span class="filepond--label-action">Browse</span>',
                        labelFileWaitingForSize: 'Menunggu ukuran file',
                        labelFileSizeNotAvailable: 'Ukuran file tidak tersedia',
                        labelFileLoading: 'Loading',
                        labelFileLoadError: 'Error saat loading',
                        labelFileProcessing: 'Uploading',
                        labelFileProcessingComplete: 'Upload selesai',
                        labelFileProcessingAborted: 'Upload dibatalkan',
                        labelFileProcessingError: 'Error saat upload',
                        labelFileProcessingRevertError: 'Error saat revert',
                        labelFileRemoveError: 'Error saat hapus',
                        labelTapToCancel: 'tap untuk batal',
                        labelTapToRetry: 'tap untuk coba lagi',
                        labelTapToUndo: 'tap untuk undo',
                        labelButtonRemoveItem: 'Hapus',
                        labelButtonAbortItemLoad: 'Batal',
                        labelButtonRetryItemLoad: 'Coba lagi',
                        labelButtonAbortItemProcessing: 'Batal',
                        labelButtonUndoItemProcessing: 'Undo',
                        labelButtonRetryItemProcessing: 'Coba lagi',
                        labelButtonProcessItem: 'Upload',
                        labelMaxFileSizeExceeded: 'File terlalu besar',
                        labelMaxFileSize: 'Ukuran file maksimal {filesize}',
                        labelMaxTotalFileSizeExceeded: 'Total ukuran file terlalu besar',
                        labelMaxTotalFileSize: 'Total ukuran file maksimal {filesize}',
                        labelFileTypeNotAllowed: 'Jenis file tidak diizinkan',
                        fileValidateTypeLabelExpectedTypes: 'Expects {allButLastType} or {lastType}',
                        imagePreviewHeight: 150,
                        stylePanelLayout: 'compact',
                        styleLoadIndicatorPosition: 'center bottom',
                        styleProgressIndicatorPosition: 'right bottom',
                        styleButtonRemoveItemPosition: 'left bottom',
                        styleButtonProcessItemPosition: 'right bottom'
                    });

                    element.filePond = pond;
                });
            }
        </script>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Web\perizinan-pasar-next\resources\views/pages/permohonan/baru.blade.php ENDPATH**/ ?>